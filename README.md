# CS2 Cheat Menu - Modern Animated Interface

Um menu moderno e animado para cheat de CS2, desenvolvido com DirectX9 + ImGui e Visual Studio 2022.

## 🎨 Características

- **Design Moderno**: Interface minimalista com tema escuro
- **Animações Suaves**: Fade in/out, hover effects e transições
- **Componentes Customizados**: Botões, sliders e checkboxes animados
- **Sistema de Abas**: Organização intuitiva (Aimbot, Visuals, Misc, Config)
- **Responsivo**: Layout adaptável e bem estruturado

## 🛠️ Requisitos

- Visual Studio 2022
- Windows SDK 10.0
- DirectX 9 SDK
- ImGui (pasta imgui/ no diretório do projeto)

## 📁 Estrutura do Projeto

```
CS2CheatMenu/
├── CS2CheatMenu.sln          # Solution file
├── CS2CheatMenu.vcxproj      # Projeto VS2022
├── main.cpp                  # Código principal
├── menu_style.h              # Estilos e cores
├── animations.h              # Sistema de animações
├── menu_components.h         # Componentes customizados
├── imgui/                    # Biblioteca ImGui
│   ├── imgui.h
│   ├── imgui.cpp
│   ├── imgui_impl_dx9.h
│   ├── imgui_impl_dx9.cpp
│   ├── imgui_impl_win32.h
│   ├── imgui_impl_win32.cpp
│   └── ... (outros arquivos ImGui)
└── README.md
```

## 🚀 Como Compilar

1. **Baixar ImGui**:
   - Vá para https://github.com/ocornut/imgui
   - Baixe a versão mais recente
   - Extraia na pasta `imgui/` do projeto

2. **Abrir no Visual Studio 2022**:
   - Abra o arquivo `CS2CheatMenu.sln`
   - Selecione a configuração (Debug/Release)
   - Compile (Ctrl+Shift+B)

3. **Executar**:
   - Execute o projeto (F5)
   - Use INSERT para mostrar/ocultar o menu

## 🎮 Funcionalidades

### Aimbot
- Enable/Disable com toggle animado
- FOV slider colorido
- Smoothing configurável
- Seleção de bone (Head/Chest/Body)
- Trigger Bot com delay

### Visuals
- ESP completo (Box, Name, Health, Distance)
- Wallhack com glow e chams
- Configurações de mundo (No Flash, No Smoke, Brightness)

### Misc
- Bunny Hop
- Auto Strafe
- Speed Hack com fator configurável
- Radar Hack
- Rank Reveal

### Config
- Sistema de save/load
- Múltiplos perfis
- Interface de gerenciamento

## 🎨 Customização

### Cores
Edite `menu_style.h` para alterar o esquema de cores:

```cpp
static constexpr ImVec4 Accent = ImVec4(0.26f, 0.59f, 0.98f, 1.00f); // Azul
static constexpr ImVec4 Success = ImVec4(0.20f, 0.80f, 0.20f, 1.00f); // Verde
```

### Animações
Configure velocidades em `animations.h`:

```cpp
SetTarget("menu_alpha", visible ? 1.0f : 0.0f, 6.0f); // Velocidade 6x
```

### Componentes
Adicione novos componentes em `menu_components.h`.

## 🔧 Integração com CS2

Para usar como overlay no CS2:

1. **Modo Overlay**: Modifique as flags da janela para overlay transparente
2. **Hook DirectX**: Implemente hook no DirectX do CS2
3. **Memory Reading**: Adicione leitura de memória para funcionalidades
4. **Input Handling**: Configure captura de input global

## ⚠️ Aviso Legal

Este projeto é apenas para fins educacionais. O uso de cheats em jogos online pode resultar em banimento permanente. Use por sua própria conta e risco.

## 📝 Licença

Este projeto é fornecido "como está" sem garantias. Use responsavelmente.

## 🤝 Contribuições

Contribuições são bem-vindas! Sinta-se livre para:
- Reportar bugs
- Sugerir melhorias
- Adicionar novas funcionalidades
- Melhorar a documentação

---

**Desenvolvido com ❤️ para a comunidade CS2**
