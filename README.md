# CS2 Cheat Menu - Professional Minimalist Interface

Um menu profissional e minimalista para cheat de CS2, desenvolvido com DirectX9 + ImGui e Visual Studio 2022.

## 🎨 Características

- **Design Minimalista**: Interface profissional com paleta de cores sutil
- **Fonte Inter Personalizada**: Tipografia moderna e legível com fallbacks inteligentes
- **Menu Movível**: Sistema de drag & drop com feedback visual na barra de título
- **Animações Suaves**: Fade in/out, hover effects e transições profissionais
- **Componentes Customizados**: Botões, sliders e checkboxes com design limpo
- **Sistema de Abas**: Organização intuitiva (Aimbot, Visuals, Misc, Config)
- **Layout Responsivo**: Interface adaptável e bem estruturada

## ✨ Novas Funcionalidades

### 🎨 Design Minimalista e Profissional
- Paleta de cores refinada com tons de cinza sutis
- Tipografia Inter personalizada para máxima legibilidade
- Espaçamentos otimizados para uma experiência limpa
- Elementos visuais reduzidos ao essencial

### 🖱️ Menu Movível
- Arraste o menu pela barra de título
- Feedback visual com mudança de cor ao passar o mouse
- Indicador de movimento (pontos) que aparece no hover
- Limitação automática dentro da área da tela

### 🔤 Sistema de Fontes Inteligente
- Carregamento automático da fonte Inter se disponível
- Fallbacks para Segoe UI Variable (Windows 11) e Segoe UI
- Suporte a fontes incorporadas para distribuição
- Configuração otimizada para clareza em diferentes tamanhos

## 🛠️ Requisitos

- Visual Studio 2022
- Windows SDK 10.0
- DirectX 9 SDK
- ImGui (pasta imgui/ no diretório do projeto)
- Fonte Inter (baixada automaticamente ou instalada no sistema)

## 📁 Estrutura do Projeto

```
CS2CheatMenu/
├── CS2CheatMenu.sln          # Solution file
├── CS2CheatMenu.vcxproj      # Projeto VS2022
├── main.cpp                  # Código principal
├── menu_style.h              # Estilos e cores minimalistas
├── modern_ui.h               # Componentes UI modernos
├── animations.h              # Sistema de animações
├── menu_components.h         # Componentes customizados
├── font_loader.h             # Sistema de carregamento de fontes
├── inter_font_data.h         # Dados da fonte Inter incorporada
├── notifications.h           # Sistema de notificações
├── download_inter_font.bat   # Script para baixar fonte Inter
├── fonts/                    # Diretório de fontes personalizadas
│   ├── Inter-Regular.ttf     # Fonte Inter Regular
│   ├── Inter-Medium.ttf      # Fonte Inter Medium
│   └── Inter-Bold.ttf        # Fonte Inter Bold
├── imgui/                    # Biblioteca ImGui
│   ├── imgui.h
│   ├── imgui.cpp
│   ├── imgui_impl_dx9.h
│   ├── imgui_impl_dx9.cpp
│   ├── imgui_impl_win32.h
│   ├── imgui_impl_win32.cpp
│   └── ... (outros arquivos ImGui)
└── README.md
```

## 🚀 Como Compilar

1. **Baixar ImGui**:
   - Vá para https://github.com/ocornut/imgui
   - Baixe a versão mais recente
   - Extraia na pasta `imgui/` do projeto

2. **Instalar Fonte Inter (Opcional)**:
   - Execute `download_inter_font.bat` para baixar automaticamente
   - Ou instale manualmente no sistema a partir de https://fonts.google.com/specimen/Inter
   - O sistema usará fallbacks se a fonte não estiver disponível

3. **Abrir no Visual Studio 2022**:
   - Abra o arquivo `CS2CheatMenu.sln`
   - Selecione a configuração (Debug/Release)
   - Compile (Ctrl+Shift+B)

4. **Executar**:
   - Execute o projeto (F5)
   - Use INSERT para mostrar/ocultar o menu

## 🎮 Como Usar as Novas Funcionalidades

### 🖱️ Movimentação do Menu
- **Arrastar**: Clique e arraste na barra de título para mover o menu
- **Indicador Visual**: Pontos aparecem no canto direito quando você pode arrastar
- **Feedback**: A barra de título muda de cor ao passar o mouse
- **Limitação**: O menu não pode ser movido para fora da tela

### 🔤 Sistema de Fontes
- A fonte Inter será carregada automaticamente se estiver disponível
- Fallbacks inteligentes garantem boa legibilidade em qualquer sistema
- Três tamanhos de fonte: principal (14px), título (16px) e cabeçalho (20px)

### 🎨 Interface Minimalista
- Cores sutis e profissionais para reduzir fadiga visual
- Espaçamentos otimizados para melhor organização
- Elementos visuais reduzidos ao essencial

## 🎮 Funcionalidades

### Aimbot
- Enable/Disable com toggle animado
- FOV slider colorido
- Smoothing configurável
- Seleção de bone (Head/Chest/Body)
- Trigger Bot com delay

### Visuals
- ESP completo (Box, Name, Health, Distance)
- Wallhack com glow e chams
- Configurações de mundo (No Flash, No Smoke, Brightness)

### Misc
- Bunny Hop
- Auto Strafe
- Speed Hack com fator configurável
- Radar Hack
- Rank Reveal

### Config
- Sistema de save/load
- Múltiplos perfis
- Interface de gerenciamento

## 🎨 Customização

### Cores
Edite `menu_style.h` para alterar o esquema de cores:

```cpp
static constexpr ImVec4 Accent = ImVec4(0.26f, 0.59f, 0.98f, 1.00f); // Azul
static constexpr ImVec4 Success = ImVec4(0.20f, 0.80f, 0.20f, 1.00f); // Verde
```

### Animações
Configure velocidades em `animations.h`:

```cpp
SetTarget("menu_alpha", visible ? 1.0f : 0.0f, 6.0f); // Velocidade 6x
```

### Componentes
Adicione novos componentes em `menu_components.h`.

## 🔧 Integração com CS2

Para usar como overlay no CS2:

1. **Modo Overlay**: Modifique as flags da janela para overlay transparente
2. **Hook DirectX**: Implemente hook no DirectX do CS2
3. **Memory Reading**: Adicione leitura de memória para funcionalidades
4. **Input Handling**: Configure captura de input global

## ⚠️ Aviso Legal

Este projeto é apenas para fins educacionais. O uso de cheats em jogos online pode resultar em banimento permanente. Use por sua própria conta e risco.

## 📝 Licença

Este projeto é fornecido "como está" sem garantias. Use responsavelmente.

## 🤝 Contribuições

Contribuições são bem-vindas! Sinta-se livre para:
- Reportar bugs
- Sugerir melhorias
- Adicionar novas funcionalidades
- Melhorar a documentação

---

**Desenvolvido com ❤️ para a comunidade CS2**
