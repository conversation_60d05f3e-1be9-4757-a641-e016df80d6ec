#pragma once
#include "imgui.h"
#include <algorithm>
#include <unordered_map>
#include <string>
#include <cmath>

namespace Animations {
    // Sistema de animação global aprimorado
    class AnimationSystem {
    private:
        std::unordered_map<std::string, float> m_Values;
        std::unordered_map<std::string, float> m_Targets;
        std::unordered_map<std::string, float> m_Speeds;
        std::unordered_map<std::string, int> m_AnimTypes; // 0=linear, 1=easeOut, 2=easeInOut, 3=bounce
        
    public:
        // Definir um valor animado com tipo de animação
        void SetTarget(const std::string& key, float target, float speed = 5.0f, int animType = 1) {
            m_Targets[key] = target;
            m_Speeds[key] = speed;
            m_AnimTypes[key] = animType;
            
            // Inicializar valor se não existir
            if (m_Values.find(key) == m_Values.end()) {
                m_Values[key] = target;
            }
        }
        
        // Obter valor atual (animado)
        float GetValue(const std::string& key) {
            if (m_Values.find(key) == m_Values.end()) {
                return 0.0f;
            }
            return m_Values[key];
        }
        
        // Verificar se a animação está completa
        bool IsComplete(const std::string& key) {
            if (m_Values.find(key) == m_Values.end() || m_Targets.find(key) == m_Values.end()) {
                return true;
            }
            return std::abs(m_Values[key] - m_Targets[key]) < 0.001f;
        }
        
        // Funções de easing
        float EaseOutQuad(float t) {
            return t * (2.0f - t);
        }
        
        float EaseInOutQuad(float t) {
            return t < 0.5f ? 2.0f * t * t : -1.0f + (4.0f - 2.0f * t) * t;
        }
        
        float Bounce(float t) {
            return std::abs(std::sin(6.28f * (t + 1.0f) * (t + 1.0f)) * (1.0f - t));
        }
        
        // Atualizar todas as animações
        void Update() {
            float deltaTime = ImGui::GetIO().DeltaTime;
            
            for (auto& pair : m_Targets) {
                const std::string& key = pair.first;
                float target = pair.second;
                float& current = m_Values[key];
                float speed = m_Speeds[key];
                int animType = m_AnimTypes[key];
                
                // Interpolação baseada no tipo de animação
                float diff = target - current;
                if (std::abs(diff) > 0.001f) {
                    float step = diff * speed * deltaTime;
                    
                    // Aplicar easing baseado no tipo
                    if (animType == 1) { // EaseOut
                        float t = 1.0f - std::abs(diff) / (std::abs(target - current) + 0.0001f);
                        step *= EaseOutQuad(t);
                    } else if (animType == 2) { // EaseInOut
                        float t = 1.0f - std::abs(diff) / (std::abs(target - current) + 0.0001f);
                        step *= EaseInOutQuad(t);
                    } else if (animType == 3) { // Bounce
                        float t = 1.0f - std::abs(diff) / (std::abs(target - current) + 0.0001f);
                        step *= (1.0f + 0.5f * Bounce(t));
                    }
                    
                    current += step;
                    
                    // Evitar oscilação
                    if ((diff > 0 && current > target) || (diff < 0 && current < target)) {
                        current = target;
                    }
                } else {
                    current = target;
                }
            }
        }
    };
    
    // Instância global
    static AnimationSystem g_AnimSystem;
    
    // Funções de conveniência
    static void SetTarget(const std::string& key, float target, float speed = 5.0f, int animType = 1) {
        g_AnimSystem.SetTarget(key, target, speed, animType);
    }
    
    static float GetValue(const std::string& key) {
        return g_AnimSystem.GetValue(key);
    }
    
    static bool IsComplete(const std::string& key) {
        return g_AnimSystem.IsComplete(key);
    }
    
    static void Update() {
        g_AnimSystem.Update();
    }
    
    // Animação específica para o menu principal
    namespace Menu {
        static float g_Alpha = 0.0f;
        static bool g_IsVisible = false;
        static ImVec2 g_Position = ImVec2(0, 0);
        static bool g_IsDragging = false;
        
        static void SetVisible(bool visible) {
            g_IsVisible = visible;
            SetTarget("menu_alpha", visible ? 1.0f : 0.0f, 6.0f, 1); // EaseOut
        }
        
        static void Update() {
            g_Alpha = GetValue("menu_alpha");
        }
        
        static bool ShouldRender() {
            return g_Alpha > 0.01f;
        }
        
        static float GetAlpha() {
            return g_Alpha;
        }
        
        // Funções para movimentação do menu
        static void StartDragging() {
            g_IsDragging = true;
        }
        
        static void StopDragging() {
            g_IsDragging = false;
        }
        
        static bool IsDragging() {
            return g_IsDragging;
        }
        
        static void SetPosition(ImVec2 pos) {
            g_Position = pos;
        }
        
        static ImVec2 GetPosition() {
            return g_Position;
        }
    }
    
    // Animações para elementos específicos
    namespace Elements {
        // Botão animado com efeitos
        static bool AnimatedButton(const char* label, const ImVec2& size = ImVec2(0, 0)) {
            std::string key = std::string("btn_") + label;
            
            // Verificar hover
            bool hovered = ImGui::IsItemHovered();
            bool active = ImGui::IsItemActive();
            
            // Configurar animação
            SetTarget(key, hovered ? 1.0f : 0.0f, 8.0f, 1); // EaseOut
            float animValue = GetValue(key);
            
            // Efeito de clique
            std::string clickKey = key + "_click";
            if (active) {
                SetTarget(clickKey, 1.0f, 12.0f, 3); // Bounce
            } else {
                SetTarget(clickKey, 0.0f, 8.0f, 1); // EaseOut
            }
            float clickAnim = GetValue(clickKey);
            
            // Interpolar cores
            ImVec4 normalColor = ImGui::GetStyleColorVec4(ImGuiCol_Button);
            ImVec4 hoveredColor = ImGui::GetStyleColorVec4(ImGuiCol_ButtonHovered);
            ImVec4 currentColor = ImVec4(
                normalColor.x + (hoveredColor.x - normalColor.x) * animValue,
                normalColor.y + (hoveredColor.y - normalColor.y) * animValue,
                normalColor.z + (hoveredColor.z - normalColor.z) * animValue,
                normalColor.w + (hoveredColor.w - normalColor.w) * animValue
            );
            
            // Efeito de escala no clique
            float scale = 1.0f - clickAnim * 0.05f;
            ImVec2 buttonSize = size;
            if (buttonSize.x == 0) buttonSize = ImVec2(ImGui::CalcTextSize(label).x + 20, ImGui::GetFrameHeight());
            ImVec2 scaledSize = ImVec2(buttonSize.x * scale, buttonSize.y * scale);
            
            // Centralizar o botão redimensionado
            float offsetX = (buttonSize.x - scaledSize.x) * 0.5f;
            float offsetY = (buttonSize.y - scaledSize.y) * 0.5f;
            ImGui::SetCursorPos(ImVec2(ImGui::GetCursorPosX() + offsetX, ImGui::GetCursorPosY() + offsetY));
            
            // Renderizar botão com cor animada
            ImGui::PushStyleColor(ImGuiCol_Button, currentColor);
            bool clicked = ImGui::Button(label, scaledSize);
            ImGui::PopStyleColor();
            
            return clicked;
        }
        
        // Tab animada
        static bool AnimatedTab(const char* label, bool selected, const ImVec2& size = ImVec2(0, 0)) {
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            ImVec2 pos = ImGui::GetCursorScreenPos();
            ImVec2 tabSize = (size.x == 0 && size.y == 0) ? 
                             ImVec2(ImGui::CalcTextSize(label).x + 20, 30) : size;
            
            // Chave única para esta tab
            std::string key = std::string("tab_") + label;
            
            // Botão invisível para detecção de clique
            bool clicked = ImGui::InvisibleButton(label, tabSize);
            bool hovered = ImGui::IsItemHovered();
            
            // Configurar animação
            SetTarget(key, selected ? 1.0f : 0.0f, 6.0f, 2); // EaseInOut
            float animValue = GetValue(key);
            
            // Animação de hover
            std::string hoverKey = key + "_hover";
            SetTarget(hoverKey, hovered && !selected ? 1.0f : 0.0f, 8.0f, 1); // EaseOut
            float hoverAnim = GetValue(hoverKey);
            
            // Cores
            ImU32 bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(
                0.12f + 0.08f * animValue + 0.05f * hoverAnim,
                0.12f + 0.08f * animValue + 0.05f * hoverAnim,
                0.15f + 0.10f * animValue + 0.05f * hoverAnim,
                0.9f + 0.1f * animValue
            ));
            
            ImU32 textColor = ImGui::ColorConvertFloat4ToU32(ImVec4(
                0.7f + 0.3f * animValue + 0.1f * hoverAnim,
                0.7f + 0.3f * animValue + 0.1f * hoverAnim,
                0.7f + 0.3f * animValue + 0.1f * hoverAnim,
                1.0f
            ));
            
            // Desenhar fundo da tab
            draw_list->AddRectFilled(
                pos, 
                ImVec2(pos.x + tabSize.x, pos.y + tabSize.y), 
                bgColor, 
                8.0f
            );
            
            // Indicador de seleção animado
            if (animValue > 0.01f) {
                float indicatorHeight = 3.0f * animValue;
                draw_list->AddRectFilled(
                    ImVec2(pos.x, pos.y + tabSize.y - indicatorHeight),
                    ImVec2(pos.x + tabSize.x, pos.y + tabSize.y),
                    ImGui::ColorConvertFloat4ToU32(ImVec4(0.26f, 0.59f, 0.98f, animValue)),
                    0.0f, 0.0f
                );
            }
            
            // Texto centralizado
            ImVec2 textSize = ImGui::CalcTextSize(label);
            ImVec2 textPos = ImVec2(
                pos.x + (tabSize.x - textSize.x) * 0.5f,
                pos.y + (tabSize.y - textSize.y) * 0.5f
            );
            draw_list->AddText(textPos, textColor, label);
            
            return clicked;
        }
        
        // Toggle animado
        static bool AnimatedToggle(const char* label, bool* v) {
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            ImVec2 pos = ImGui::GetCursorScreenPos();
            ImVec2 size = ImVec2(50, 24);
            
            // Área clicável
            ImGui::InvisibleButton(label, size);
            bool clicked = ImGui::IsItemClicked();
            bool hovered = ImGui::IsItemHovered();
            
            if (clicked) *v = !(*v);
            
            // Chave para animação
            std::string key = std::string("toggle_") + label;
            SetTarget(key, *v ? 1.0f : 0.0f, 8.0f, 2); // EaseInOut
            float animValue = GetValue(key);
            
            // Cores baseadas no estado
            ImU32 bgColor;
            if (*v) {
                bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(
                    0.26f, 0.59f, 0.98f, 0.7f + 0.3f * animValue
                ));
            } else {
                bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(
                    0.2f, 0.2f, 0.2f, 0.5f + 0.3f * (1.0f - animValue)
                ));
            }
            
            if (hovered) {
                bgColor = ImGui::ColorConvertFloat4ToU32(ImVec4(
                    0.26f + 0.1f * animValue, 
                    0.59f * animValue + 0.2f * (1.0f - animValue), 
                    0.98f * animValue + 0.2f * (1.0f - animValue), 
                    0.8f
                ));
            }
            
            // Desenhar fundo do toggle
            float radius = size.y * 0.5f;
            draw_list->AddRectFilled(pos, ImVec2(pos.x + size.x, pos.y + size.y), bgColor, radius);
            
            // Desenhar handle (círculo) com animação
            float handleRadius = radius - 2;
            float handleX = pos.x + radius + animValue * (size.x - radius * 2);
            ImVec2 handleCenter = ImVec2(handleX, pos.y + radius);
            
            // Efeito de brilho ao redor do handle
            if (*v) {
                draw_list->AddCircleFilled(
                    handleCenter, 
                    handleRadius + 2.0f * animValue, 
                    ImGui::ColorConvertFloat4ToU32(ImVec4(1.0f, 1.0f, 1.0f, 0.2f * animValue))
                );
            }
            
            draw_list->AddCircleFilled(handleCenter, handleRadius, IM_COL32(255, 255, 255, 255));
            
            // Label ao lado
            ImGui::SameLine();
            ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 3); // Alinhar verticalmente
            
            // Cor do texto baseada no estado
            ImGui::PushStyleColor(ImGuiCol_Text, *v ? 
                ImVec4(0.26f, 0.98f, 0.59f, 0.8f + 0.2f * animValue) : 
                ImVec4(0.7f, 0.7f, 0.7f, 0.8f));
            ImGui::Text("%s", label);
            ImGui::PopStyleColor();
            
            return clicked;
        }
    }
}
