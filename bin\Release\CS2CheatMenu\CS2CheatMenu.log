﻿  main.cpp
C:\Users\<USER>\Desktop\menu cheat\animations.h(287,27): warning C4244: 'argumento': conversão de 'float' para 'ImDrawFlags', possível perda de dados
  (compilando o arquivo fonte 'main.cpp')
  
C:\Users\<USER>\Desktop\menu cheat\menu_components.h(244,25): warning C4244: 'inicializando': conversão de 'double' para 'float', possível perda de dados
  (compilando o arquivo fonte 'main.cpp')
  
C:\Users\<USER>\Desktop\menu cheat\main.cpp(109,21): error C2664: 'bool CustomGUI::Initialize(HWND)': não é possível converter um argumento 1 de 'LPDIRECT3DDEVICE9' em 'HWND'
      C:\Users\<USER>\Desktop\menu cheat\main.cpp(109,32):
      Tipos apontados não são relacionados; conversão requer reinterpret_cast, conversão C-style ou conversão function-style entre parênteses
      C:\Users\<USER>\Desktop\menu cheat\custom_gui.h(73,10):
      consulte a declaração de 'CustomGUI::Initialize'
      C:\Users\<USER>\Desktop\menu cheat\main.cpp(109,21):
      ao tentar corresponder a lista de argumentos '(LPDIRECT3DDEVICE9)'
  
C:\Users\<USER>\Desktop\menu cheat\main.cpp(110,14): error C2039: ' cout': não é um membro de 'std'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\fstream(20,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\menu cheat\main.cpp(110,14): error C2065: 'cout': identificador não declarado
