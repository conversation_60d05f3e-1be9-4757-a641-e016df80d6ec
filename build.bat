@echo off
echo ========================================
echo    Compilando CS2 Cheat Menu
echo ========================================
echo.

REM Tentar encontrar MSBuild
set "MSBUILD_PATH="

REM Visual Studio 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
)

REM Visual Studio 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)

REM Visual Studio 2022 Enterprise
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

if "%MSBUILD_PATH%"=="" (
    echo [ERRO] MSBuild nao encontrado!
    echo Certifique-se de ter o Visual Studio 2022 instalado.
    pause
    exit /b 1
)

echo [INFO] Usando MSBuild: %MSBUILD_PATH%
echo.

echo Compilando em modo Debug...
"%MSBUILD_PATH%" CS2CheatMenu.sln /p:Configuration=Debug /p:Platform=Win32 /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo        Compilacao Concluida!
    echo ========================================
    echo.
    echo Executavel criado em: bin\Debug\CS2CheatMenu.exe
    echo.
    echo Pressione qualquer tecla para executar...
    pause >nul
    
    if exist "bin\Debug\CS2CheatMenu.exe" (
        start "" "bin\Debug\CS2CheatMenu.exe"
    ) else (
        echo [ERRO] Executavel nao encontrado!
    )
) else (
    echo.
    echo ========================================
    echo         Erro na Compilacao!
    echo ========================================
    echo.
    echo Verifique os erros acima e tente novamente.
    pause
)
