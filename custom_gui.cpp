#include "custom_gui.h"
#include <iostream>
#include <cmath>
#include <algorithm>
#include <map>
#include <string>

namespace CustomGUI {
    // Variáveis globais
    HDC g_hdc = nullptr;
    HFONT hFontTitle = nullptr;      // Arial Black 28px
    HFONT hFontSection = nullptr;    // Arial Bold 18px
    HFONT hFontButton = nullptr;     // Inter Medium 14px
    HFONT hFontSmall = nullptr;      // Inter Regular 12px

    // Sistema de input global
    static POINT g_mousePos = {0, 0};
    static bool g_mousePressed = false;
    static bool g_mouseReleased = false;
    static bool g_lastMouseState = false;
    static bool g_mouseDown = false;
    HWND g_hwnd = nullptr;
    POINT cursorPos = {0, 0};
    
    // Posição do menu
    int menuX = 100;
    int menuY = 100;
    
    // Variáveis de layout
    int spacing = 0;
    int childSpacing = 0;
    int groupX = 0;
    int groupY = 0;
    int holdPrevX = 0;
    int holdPrevY = 0;
    
    // Estados
    bool comboClicked = false;
    bool isDragging = false;
    POINT dragOffset = {0, 0};
    
    // Cores do tema Neverlose (com transparência para overlay)
    Color menuBG(0, 0, 0, 191);           // rgba(0, 0, 0, 0.75)
    Color sideBar(14, 12, 16, 191);       // rgba(14, 12, 16, 0.75)
    Color accent(82, 123, 251, 255);      // #527BFB
    Color light(25, 25, 35, 128);         // rgba(25, 25, 35, 0.5)
    Color groupBG(10, 10, 18, 128);       // rgba(10, 10, 18, 0.5)
    Color textColor(255, 255, 255, 255);  // #FFFFFF
    Color textSecondary(255, 255, 255, 128); // rgba(255, 255, 255, 0.5)
    Color textTertiary(255, 255, 255, 64);   // rgba(255, 255, 255, 0.25)
    
    // Estados das páginas
    bool firstPage = true;
    bool secondPage = false;
    bool thirdPage = false;
    
    // Implementação das funções base
    void DrawRect(int x, int y, int w, int h, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HBRUSH brush = CreateSolidBrush(color.ToWin32());
        RECT rect = { x, y, x + w, y + h };
        FillRect(g_hdc, &rect, brush);
        DeleteObject(brush);
    }

    void DrawRectOutline(int x, int y, int w, int h, Color color, int thickness) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, thickness, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);
        HBRUSH oldBrush = (HBRUSH)SelectObject(g_hdc, GetStockObject(NULL_BRUSH));

        Rectangle(g_hdc, x, y, x + w, y + h);

        SelectObject(g_hdc, oldBrush);
        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawRoundedRect(int x, int y, int w, int h, int radius, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HBRUSH brush = CreateSolidBrush(color.ToWin32());
        HBRUSH oldBrush = (HBRUSH)SelectObject(g_hdc, brush);
        HPEN pen = CreatePen(PS_SOLID, 1, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        RoundRect(g_hdc, x, y, x + w, y + h, radius, radius);

        SelectObject(g_hdc, oldBrush);
        SelectObject(g_hdc, oldPen);
        DeleteObject(brush);
        DeleteObject(pen);
    }

    void DrawMessage(HFONT font, int x, int y, int alpha, const char* message) {
        // Coordenadas diretas da janela (sem offset)

        HFONT oldFont = (HFONT)SelectObject(g_hdc, font);
        SetTextColor(g_hdc, textColor.ToWin32());
        SetBkMode(g_hdc, TRANSPARENT);

        TextOutA(g_hdc, x, y, message, (int)strlen(message));
        SelectObject(g_hdc, oldFont);
    }

    int GetTextWidth(const char* text, HFONT font) {
        HFONT oldFont = (HFONT)SelectObject(g_hdc, font);
        SIZE size;
        GetTextExtentPoint32A(g_hdc, text, (int)strlen(text), &size);
        SelectObject(g_hdc, oldFont);
        return size.cx;
    }
    
    bool Initialize(HWND hwnd) {
        g_hwnd = hwnd;
        g_hdc = GetDC(hwnd);

        // Criar fontes profissionais customizadas
        hFontTitle = CreateFontA(
            28,                        // Height - Título grande
            0,                         // Width
            0,                         // Escapement
            0,                         // Orientation
            FW_BLACK,                  // Weight - Extra Bold
            FALSE,                     // Italic
            FALSE,                     // Underline
            FALSE,                     // StrikeOut
            DEFAULT_CHARSET,           // CharSet
            OUT_DEFAULT_PRECIS,        // OutPrecision
            CLIP_DEFAULT_PRECIS,       // ClipPrecision
            CLEARTYPE_QUALITY,         // Quality
            DEFAULT_PITCH | FF_SWISS,  // PitchAndFamily
            "Arial Black"              // FaceName - Arial Black
        );

        hFontSection = CreateFontA(
            16,                        // Height - Seções
            0,                         // Width
            0,                         // Escapement
            0,                         // Orientation
            FW_BOLD,                   // Weight - Bold
            FALSE,                     // Italic
            FALSE,                     // Underline
            FALSE,                     // StrikeOut
            DEFAULT_CHARSET,           // CharSet
            OUT_DEFAULT_PRECIS,        // OutPrecision
            CLIP_DEFAULT_PRECIS,       // ClipPrecision
            CLEARTYPE_QUALITY,         // Quality
            DEFAULT_PITCH | FF_SWISS,  // PitchAndFamily
            "Arial"                    // FaceName - Arial Bold
        );

        hFontButton = CreateFontA(
            14,                        // Height - Botões
            0,                         // Width
            0,                         // Escapement
            0,                         // Orientation
            FW_MEDIUM,                 // Weight - Medium
            FALSE,                     // Italic
            FALSE,                     // Underline
            FALSE,                     // StrikeOut
            DEFAULT_CHARSET,           // CharSet
            OUT_DEFAULT_PRECIS,        // OutPrecision
            CLIP_DEFAULT_PRECIS,       // ClipPrecision
            CLEARTYPE_QUALITY,         // Quality
            DEFAULT_PITCH | FF_DONTCARE, // PitchAndFamily
            "Inter"                    // FaceName - Inter Medium
        );

        hFontSmall = CreateFontA(
            12,                        // Height - Texto pequeno
            0,                         // Width
            0,                         // Escapement
            0,                         // Orientation
            FW_NORMAL,                 // Weight - Regular
            FALSE,                     // Italic
            FALSE,                     // Underline
            FALSE,                     // StrikeOut
            DEFAULT_CHARSET,           // CharSet
            OUT_DEFAULT_PRECIS,        // OutPrecision
            CLIP_DEFAULT_PRECIS,       // ClipPrecision
            CLEARTYPE_QUALITY,         // Quality
            DEFAULT_PITCH | FF_DONTCARE, // PitchAndFamily
            "Inter"                    // FaceName - Inter Regular
        );

        return (hFontTitle != nullptr && hFontSection != nullptr && hFontButton != nullptr && hFontSmall != nullptr);
    }

    void Cleanup() {
        if (hFontTitle) {
            DeleteObject(hFontTitle);
            hFontTitle = nullptr;
        }
        if (hFontSection) {
            DeleteObject(hFontSection);
            hFontSection = nullptr;
        }
        if (hFontButton) {
            DeleteObject(hFontButton);
            hFontButton = nullptr;
        }
        if (hFontSmall) {
            DeleteObject(hFontSmall);
            hFontSmall = nullptr;
        }
        if (g_hdc && g_hwnd) {
            ReleaseDC(g_hwnd, g_hdc);
            g_hdc = nullptr;
        }
    }
    
    bool IsMouseOver(int x, int y, int w, int h) {
        return (g_mousePos.x >= x && g_mousePos.x <= x + w &&
                g_mousePos.y >= y && g_mousePos.y <= y + h);
    }
    
    void Sleep(int ms) {
        ::Sleep(ms);
    }
    
    void HandleMenuDrag() {
        GetCursorPos(&cursorPos);
        
        // Área de drag (barra superior do menu)
        if (IsMouseOver(0, -5, 500, 25)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) {
                if (!isDragging) {
                    isDragging = true;
                    dragOffset.x = cursorPos.x - menuX;
                    dragOffset.y = cursorPos.y - menuY;
                }
            }
        }
        
        if (isDragging) {
            if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) {
                menuX = cursorPos.x - dragOffset.x;
                menuY = cursorPos.y - dragOffset.y;
            } else {
                isDragging = false;
            }
        }
    }
    
    void ResetSpacing() {
        spacing = 0;
        childSpacing = 0;
    }

    // Implementação dos elementos da GUI
    bool DrawSideBar(const char* label, bool active, int number) {
        GetCursorPos(&cursorPos);
        Color activeColor(45, 48, 55, 255);
        Color hovered(60, 63, 70, 255);

        int buttonY = 5 + (number * 87);

        if (IsMouseOver(5, buttonY, 90, 85)) {
            DrawRect(5, buttonY, 90, 85, hovered);
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                return true;
            }
        }

        if (active) {
            DrawRect(5, buttonY, 90, 85, activeColor);
        }

        int width = GetTextWidth(label, hFontTitle);
        DrawMessage(hFontTitle, 50 - width / 2, buttonY + 36, 255, label);

        return false;
    }

    void DrawGroupBox(int width, int height, const char* label, bool sameline) {
        groupX = (sameline ? holdPrevX + 120 + 10 : 120) + 7;
        groupY = sameline ? holdPrevY : childSpacing;

        // Linha superior azul
        DrawRect(sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY - 4 : childSpacing - 4, width, 4, accent);
        // Fundo do grupo
        DrawRect(sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY : childSpacing, width, height, groupBG);

        // Título do grupo
        DrawMessage(hFontSmall, sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY - 22 : childSpacing - 22, 255, label);

        if (!sameline) {
            childSpacing += height + 25;
            holdPrevY = childSpacing - height - 30 + 4;
            holdPrevX = width;
        }
        spacing = 10;
    }

    void DrawCheckBox(bool& change, const char* label) {
        GetCursorPos(&cursorPos);

        // Desenhar checkbox
        if (change) {
            DrawRect(groupX, groupY + spacing, 15, 15, accent);
        } else {
            DrawRect(groupX, groupY + spacing, 15, 15, light);
        }

        // Verificar clique
        if (IsMouseOver(groupX, groupY + spacing, 15, 15)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                change = !change;
                Sleep(100);
            }
        }

        // Texto do label
        DrawMessage(hFontSmall, groupX + 20, groupY + spacing - 2, 255, label);
        spacing += 25; // Espaçamento maior para melhor visibilidade
    }

    void DrawDropDown(std::vector<const char*> info, int& manage, const char* label, bool& clicked) {
        GetCursorPos(&cursorPos);

        // Caixa principal do dropdown
        DrawRect(groupX, groupY + spacing, 100, 20, light);
        DrawRect(groupX + 1, groupY + 1 + spacing, 98, 18, groupBG);
        DrawMessage(hFontSmall, groupX + 3, groupY + spacing, 255, info[manage]);

        // Verificar clique na caixa principal
        if (IsMouseOver(groupX, groupY + spacing, 100, 20)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                clicked = !clicked;
                comboClicked = !comboClicked;
                Sleep(100);
            }
        }

        // Desenhar opções se aberto
        if (clicked) {
            for (int i = 0; i < (int)info.size(); i++) {
                int optionY = groupY + 20 + (i * 20) + spacing;
                DrawRect(groupX, optionY, 100, 20, light);

                if (IsMouseOver(groupX, optionY, 100, 20)) {
                    DrawRect(groupX + 1, optionY, 98, 20, light);
                    if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                        manage = i;
                        clicked = false;
                        comboClicked = false;
                        Sleep(100);
                    }
                } else {
                    DrawRect(groupX + 1, optionY, 98, 20, groupBG);
                }

                if (i == (int)info.size() - 1) {
                    DrawRect(groupX, optionY + 20 + 1, 100, 1, light);
                }
                DrawMessage(hFontSmall, groupX + 3, optionY, 255, info[i]);
            }
        }

        DrawMessage(hFontSmall, groupX + 103, groupY + spacing, 255, label);
        spacing += 30;
    }

    void DrawSlider(const char* label, int& change, int min, int max) {
        GetCursorPos(&cursorPos);

        // Converter valor para string
        std::string value_string = std::to_string(change) + ".0";

        // Verificar se está sendo arrastado
        if (IsMouseOver(groupX, groupY + spacing + 21, 178, 10) && (GetAsyncKeyState(VK_LBUTTON) & 0x8000)) {
            change = (cursorPos.x - menuX - groupX) * (max - min) / 178;
        }

        // Limitar valores
        if (change > max) change = max;
        if (change < min) change = min;

        // Calcular posição do valor
        int valueX = ((change - min) * 178 / (max - min));

        // Desenhar elementos
        DrawMessage(hFontSmall, groupX, groupY + spacing + 2, 255, label);
        DrawMessage(hFontSmall, groupX + 150, groupY + spacing + 2, 255, value_string.c_str());
        DrawRect(groupX, groupY + spacing + 21, 178, 10, sideBar);
        DrawRect(groupX, groupY + spacing + 21, valueX, 10, accent);

        spacing += 35;
    }

    void RenderMenu() {
        // Atualizar sistema de input
        UpdateInput();

        // Fundo principal do menu
        DrawRoundedRect(0, 0, 675, 500, 5, menuBG);

        // Sidebar com transparência
        DrawRect(0, 0, 175, 500, sideBar);

        // Título NEVERLOSE com fonte profissional
        DrawMessage(hFontTitle, 15, 20, 255, "NEVERLOSE");

        // Seção AIMBOT
        DrawMessage(hFontSection, 15, 70, 128, "AIMBOT");

        // Variáveis estáticas para controle de páginas
        static int selectedPage = 0; // 0=Rage, 1=Legit, 2=Visuals, etc.

        // Botão Rage com ícone
        if (DrawIconButton(10, 90, 155, 30, "Rage", DrawAimbotIcon, selectedPage == 0 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 0;
        }

        // Botão Legit com ícone
        if (DrawIconButton(10, 125, 155, 30, "Legit", DrawMouseIcon, selectedPage == 1 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 1;
        }

        // Seção COMMON
        DrawMessage(hFontSection, 15, 175, 128, "COMMON");

        // Botão Visuals com ícone
        if (DrawIconButton(10, 195, 155, 30, "Visuals", DrawEyeIcon, selectedPage == 2 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 2;
        }

        // Botão Inventory com ícone
        if (DrawIconButton(10, 230, 155, 30, "Inventory", DrawLayerIcon, selectedPage == 3 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 3;
        }

        // Botão Misc com ícone
        if (DrawIconButton(10, 265, 155, 30, "Misc", DrawListIcon, selectedPage == 4 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 4;
        }

        // Seção PRESETS
        DrawMessage(hFontSection, 15, 315, 128, "PRESETS");

        // Botão Config com ícone
        if (DrawIconButton(10, 335, 155, 30, "Config", DrawSettingsIcon, selectedPage == 5 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 5;
        }

        // Botão Script com ícone
        if (DrawIconButton(10, 370, 155, 30, "Script", DrawSaveIcon, selectedPage == 6 ? light : Color(0, 0, 0, 0))) {
            selectedPage = 6;
        }

        // User Info na parte inferior
        DrawMessage(hFontSmall, 15, 450, 191, "xewoiy");
        DrawMessage(hFontSmall, 15, 465, 255, "Alpha build v318");

        // Área principal (direita)
        DrawRoundedRect(175, 0, 500, 500, 5, sideBar);

        // Top Bar
        DrawRoundedRect(175, 0, 500, 50, 5, Color(0, 0, 0, 25));

        // Botão Save com ícone
        DrawSaveIcon(190, 15, 20, textColor);
        DrawMessage(hFontSmall, 215, 20, 255, "Save");

        // Dropdown Global
        DrawRoundedRect(280, 15, 76, 20, 5, Color(37, 38, 55, 128));
        DrawMessage(hFontSmall, 290, 20, 255, "Global");

        // Ícones da direita
        DrawListIcon(620, 15, 20, textColor);
        DrawSearchIcon(650, 15, 20, textColor);

        // Conteúdo principal baseado na página selecionada
        DrawRoundedRect(185, 60, 480, 430, 5, Color(0, 0, 0, 0));

        // Variáveis estáticas para checkboxes
        static bool enabledRage = true;
        static bool silentAim = true;
        static bool autoFire = false;
        static bool autoWall = true;
        static int fovValue = 180;
        static bool enabledLegit = false;
        static bool enabledVisuals = true;

        // Conteúdo baseado na página selecionada
        if (selectedPage == 0) { // Rage
            // Grupo MAIN com elementos profissionais
            DrawGroupPro(200, 70, 450, 180, "MAIN");

            // Toggles profissionais com animação
            DrawToggle(220, 100, "Enabled", &enabledRage);
            DrawToggle(220, 130, "Silent Aim", &silentAim);
            DrawToggle(220, 160, "Auto Fire", &autoFire);
            DrawToggle(220, 190, "Auto Wall", &autoWall);

            // Slider Field of View profissional
            static float fovFloat = 180.0f;
            DrawSliderPro(220, 220, 200, "Field of View", &fovFloat, 60.0f, 180.0f);

            // Separador
            DrawSeparator(200, 270, 450);

            // Grupo SELECTION
            DrawGroupPro(200, 290, 450, 120, "SELECTION");

            // Combo box para Target
            static int targetSelected = 0;
            static const char* targets[] = {"Head", "Body", "Legs", "Auto"};
            DrawMessage(hFontButton, 220, 320, 255, "Target Selection");
            DrawRoundedRect(220, 340, 150, 25, 3, Color(60, 60, 70, 255));
            DrawMessage(hFontButton, 230, 345, 255, targets[targetSelected]);

        } else if (selectedPage == 1) { // Legit
            // Grupo LEGIT AIMBOT
            DrawGroupPro(200, 70, 450, 200, "LEGIT AIMBOT");

            // Toggles profissionais
            static bool smoothAim = false;
            static bool rcsControl = true;
            static bool triggerBot = false;

            DrawToggle(220, 100, "Enabled", &enabledLegit);
            DrawToggle(220, 130, "Smooth Aim", &smoothAim);
            DrawToggle(220, 160, "RCS Control", &rcsControl);
            DrawToggle(220, 190, "Trigger Bot", &triggerBot);

            // Sliders para configurações
            static float smoothValue = 2.5f;
            static float rcsValue = 1.0f;
            DrawSliderPro(220, 220, 200, "Smooth Factor", &smoothValue, 1.0f, 10.0f);
            DrawSliderPro(220, 250, 200, "RCS Strength", &rcsValue, 0.0f, 2.0f);

        } else if (selectedPage == 2) { // Visuals
            DrawMessage(hFontSection, 200, 80, 64, "ESP & VISUALS");
            DrawRoundedRect(195, 95, 227, 120, 5, groupBG);

            DrawMessage(hFontSmall, 210, 110, 255, "Enabled");
            if (enabledVisuals) DrawRect(380, 110, 10, 10, accent);
            else DrawRectOutline(380, 110, 10, 10, Color(255, 255, 255, 128));

            DrawMessage(hFontSmall, 210, 130, 255, "Player ESP");
            DrawRect(380, 130, 10, 10, accent);

            DrawMessage(hFontSmall, 210, 150, 255, "Weapon ESP");
            DrawRectOutline(380, 150, 10, 10, Color(255, 255, 255, 128));

            DrawMessage(hFontSmall, 210, 170, 255, "Glow ESP");
            DrawRect(380, 170, 10, 10, accent);

        } else {
            // Outras páginas
            DrawMessage(hFontSmall, 300, 200, 128, "Selecione uma opcao na sidebar");
        }

        // Reset spacing para próximo frame
        ResetSpacing();
    }

    // Implementação dos ícones SVG
    void DrawAimbotIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Desenhar crosshair (mira)
        int center = size / 2;
        int lineLen = size / 3;

        // Linha horizontal
        MoveToEx(g_hdc, x + center - lineLen, y + center, NULL);
        LineTo(g_hdc, x + center + lineLen, y + center);

        // Linha vertical
        MoveToEx(g_hdc, x + center, y + center - lineLen, NULL);
        LineTo(g_hdc, x + center, y + center + lineLen);

        // Círculo externo
        Arc(g_hdc, x + 2, y + 2, x + size - 2, y + size - 2, x + size, y + center, x + size, y + center);

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawEyeIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);
        HBRUSH brush = CreateSolidBrush(color.ToWin32());
        HBRUSH oldBrush = (HBRUSH)SelectObject(g_hdc, brush);

        // Desenhar formato do olho
        int centerX = x + size / 2;
        int centerY = y + size / 2;

        // Elipse do olho
        Ellipse(g_hdc, x + 2, y + size/4, x + size - 2, y + 3*size/4);

        // Pupila
        SelectObject(g_hdc, GetStockObject(BLACK_BRUSH));
        Ellipse(g_hdc, centerX - 3, centerY - 3, centerX + 3, centerY + 3);

        SelectObject(g_hdc, oldBrush);
        SelectObject(g_hdc, oldPen);
        DeleteObject(brush);
        DeleteObject(pen);
    }

    void DrawMouseIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Corpo do mouse
        RoundRect(g_hdc, x + 3, y + 2, x + size - 3, y + size - 2, 8, 8);

        // Botão esquerdo
        MoveToEx(g_hdc, x + size/2, y + 2, NULL);
        LineTo(g_hdc, x + size/2, y + size/3);

        // Scroll wheel
        MoveToEx(g_hdc, x + size/2 - 2, y + size/3 + 2, NULL);
        LineTo(g_hdc, x + size/2 + 2, y + size/3 + 2);

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawLayerIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Três retângulos sobrepostos
        int offset = 3;

        // Camada de trás
        Rectangle(g_hdc, x + offset*2, y + offset*2, x + size, y + size);

        // Camada do meio
        Rectangle(g_hdc, x + offset, y + offset, x + size - offset, y + size - offset);

        // Camada da frente
        Rectangle(g_hdc, x, y, x + size - offset*2, y + size - offset*2);

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawListIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Três linhas horizontais
        int lineSpacing = size / 4;
        int startY = y + lineSpacing;

        for (int i = 0; i < 3; i++) {
            MoveToEx(g_hdc, x + 2, startY + i * lineSpacing, NULL);
            LineTo(g_hdc, x + size - 2, startY + i * lineSpacing);
        }

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawSettingsIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        int centerX = x + size / 2;
        int centerY = y + size / 2;
        int radius = size / 4;

        // Círculo central
        Ellipse(g_hdc, centerX - radius, centerY - radius, centerX + radius, centerY + radius);

        // Dentes da engrenagem (8 linhas)
        for (int i = 0; i < 8; i++) {
            double angle = i * 3.14159 / 4;
            int x1 = centerX + (int)(radius * 1.5 * cos(angle));
            int y1 = centerY + (int)(radius * 1.5 * sin(angle));
            int x2 = centerX + (int)(radius * 2 * cos(angle));
            int y2 = centerY + (int)(radius * 2 * sin(angle));

            MoveToEx(g_hdc, x1, y1, NULL);
            LineTo(g_hdc, x2, y2);
        }

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawSaveIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Disquete
        Rectangle(g_hdc, x + 2, y + 2, x + size - 2, y + size - 2);

        // Parte superior (label)
        Rectangle(g_hdc, x + 4, y + 2, x + size - 4, y + size/3);

        // Slot
        MoveToEx(g_hdc, x + size/2 - 2, y + 2, NULL);
        LineTo(g_hdc, x + size/2 + 2, y + 2);

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    void DrawSearchIcon(int x, int y, int size, Color color) {
        // Coordenadas diretas da janela (sem offset)

        HPEN pen = CreatePen(PS_SOLID, 2, color.ToWin32());
        HPEN oldPen = (HPEN)SelectObject(g_hdc, pen);

        // Lupa
        int circleSize = size * 2 / 3;
        Ellipse(g_hdc, x + 2, y + 2, x + circleSize, y + circleSize);

        // Cabo da lupa
        MoveToEx(g_hdc, x + circleSize - 3, y + circleSize - 3, NULL);
        LineTo(g_hdc, x + size - 2, y + size - 2);

        SelectObject(g_hdc, oldPen);
        DeleteObject(pen);
    }

    // Implementação de botões melhorados
    bool DrawButton(int x, int y, int w, int h, const char* text, Color bgColor) {
        static bool lastMouseState = false;
        bool clicked = false;

        // Verificar se o mouse está sobre o botão (coordenadas da janela)
        POINT mousePos;
        GetCursorPos(&mousePos);
        ScreenToClient(g_hwnd, &mousePos);

        // Coordenadas absolutas do botão na janela
        int buttonX = x;
        int buttonY = y;

        bool hovered = (mousePos.x >= buttonX && mousePos.x <= buttonX + w &&
                       mousePos.y >= buttonY && mousePos.y <= buttonY + h);

        // Verificar clique com debounce melhorado
        bool currentMouseState = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
        if (hovered && currentMouseState && !lastMouseState) {
            clicked = true;
        }
        lastMouseState = currentMouseState;

        // Desenhar fundo do botão com hover effect
        Color drawColor = bgColor;
        if (hovered) {
            drawColor = Color(
                std::min(255, bgColor.r + 30),
                std::min(255, bgColor.g + 30),
                std::min(255, bgColor.b + 30),
                bgColor.a
            );
        }

        if (bgColor.a > 0 || hovered) {
            DrawRoundedRect(x, y, w, h, 5, drawColor);
        }

        // Desenhar texto centralizado com fonte maior
        DrawMessage(hFontSmall, x + 10, y + (h - 16) / 2, 255, text);

        return clicked;
    }

    bool DrawIconButton(int x, int y, int w, int h, const char* text, void(*iconFunc)(int, int, int, Color), Color bgColor) {
        // Verificar se o mouse está sobre o botão usando sistema global
        bool hovered = IsMouseOver(x, y, w, h);

        // Verificar clique usando sistema global
        bool clicked = hovered && g_mousePressed;

        // Desenhar fundo do botão com hover effect melhorado
        Color drawColor = bgColor;
        if (hovered && bgColor.a == 0) {
            drawColor = Color(255, 255, 255, 20); // Hover sutil para botões transparentes
        } else if (hovered) {
            drawColor = light; // Usar cor light para selecionados
        }

        if (bgColor.a > 0 || hovered) {
            DrawRoundedRect(x, y, w, h, 5, drawColor);
        }

        // Desenhar ícone
        if (iconFunc) {
            iconFunc(x + 8, y + (h - 20) / 2, 20, accent);
        }

        // Desenhar texto com fonte maior
        DrawMessage(hFontSmall, x + 35, y + (h - 16) / 2, 255, text);

        return clicked;
    }

    bool DrawSideBarWithIcon(const char* text, bool active, int index, void(*iconFunc)(int, int, int, Color)) {
        return DrawIconButton(10, 90 + index * 35, 155, 30, text, iconFunc, active ? light : Color(0, 0, 0, 0));
    }

    // Sistema de toggle SIMPLES que funciona
    bool DrawToggle(int x, int y, const char* label, bool* value) {
        // Dimensões do toggle
        int toggleW = 40;
        int toggleH = 20;

        // Posição do toggle
        int toggleX = x + 150;
        int toggleY = y;

        // Obter posição do mouse DIRETAMENTE
        POINT mousePos;
        GetCursorPos(&mousePos);
        ScreenToClient(g_hwnd, &mousePos);

        // Verificar se mouse está sobre o toggle
        bool hovered = (mousePos.x >= toggleX && mousePos.x <= toggleX + toggleW &&
                       mousePos.y >= toggleY && mousePos.y <= toggleY + toggleH);

        // Verificar clique com múltiplos métodos
        static bool lastClick = false;

        // Método 1: GetAsyncKeyState
        bool currentClick1 = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;

        // Método 2: GetKeyState
        bool currentClick2 = (GetKeyState(VK_LBUTTON) & 0x8000) != 0;

        // Método 3: Usar qualquer um dos dois
        bool currentClick = currentClick1 || currentClick2;

        bool clicked = false;

        if (hovered && currentClick && !lastClick) {
            *value = !*value;  // Inverter valor
            clicked = true;
        }
        lastClick = currentClick;

        // DEBUG: Mostrar informações detalhadas
        char debugText[300];
        sprintf(debugText, "Mouse: %d,%d Toggle: %d,%d-%d,%d Hover: %s Async: %s Key: %s Click: %s Value: %s",
               mousePos.x, mousePos.y, toggleX, toggleY, toggleX+toggleW, toggleY+toggleH,
               hovered ? "YES" : "NO", currentClick1 ? "YES" : "NO", currentClick2 ? "YES" : "NO",
               clicked ? "YES" : "NO", *value ? "ON" : "OFF");
        DrawMessage(hFontSmall, x, y + 25, 255, debugText);

        // Desenhar label
        DrawMessage(hFontButton, x, y + 3, 255, label);

        // Cores baseadas no estado
        Color bgColor = Color(100, 100, 100, 255); // Padrão cinza
        if (hovered) {
            bgColor = Color(255, 0, 0, 255); // Vermelho quando hover
        } else if (*value) {
            bgColor = Color(0, 255, 0, 255); // Verde quando ativo
        }

        // Desenhar toggle
        DrawRoundedRect(toggleX, toggleY, toggleW, toggleH, 10, bgColor);

        // Desenhar knob simples
        int knobSize = 16;
        int knobX = *value ? (toggleX + toggleW - knobSize - 2) : (toggleX + 2);
        DrawRoundedRect(knobX, toggleY + 2, knobSize, knobSize, 8, Color(255, 255, 255, 255));

        return clicked;
    }

    // Slider profissional com animação
    bool DrawSliderPro(int x, int y, int w, const char* label, float* value, float min, float max) {
        static std::map<std::string, bool> isDragging;
        static std::map<std::string, bool> lastMouseState;

        std::string key = std::string(label) + "_" + std::to_string(x) + "_" + std::to_string(y);

        if (isDragging.find(key) == isDragging.end()) {
            isDragging[key] = false;
            lastMouseState[key] = false;
        }

        // Verificar mouse usando sistema global
        int sliderY = y + 20;
        int sliderH = 6;
        int knobSize = 14;

        bool hovered = (g_mousePos.x >= x && g_mousePos.x <= x + w &&
                       g_mousePos.y >= sliderY - knobSize/2 && g_mousePos.y <= sliderY + sliderH + knobSize/2);

        // Lógica de drag
        if (hovered && g_mousePressed) {
            isDragging[key] = true;
        }

        if (g_mouseReleased) {
            isDragging[key] = false;
        }

        if (isDragging[key]) {
            float percent = (float)(g_mousePos.x - x) / w;
            percent = std::max(0.0f, std::min(1.0f, percent));
            *value = min + percent * (max - min);
        }

        // Desenhar label e valor
        char valueText[32];
        sprintf(valueText, "%.1f", *value);
        DrawMessage(hFontButton, x, y, 255, label);
        DrawMessage(hFontSmall, x + w - 40, y, 180, valueText);

        // Desenhar track do slider
        Color trackColor = Color(60, 60, 70, 255);
        DrawRoundedRect(x, sliderY, w, sliderH, 3, trackColor);

        // Desenhar progresso
        float percent = (*value - min) / (max - min);
        int progressW = (int)(w * percent);
        DrawRoundedRect(x, sliderY, progressW, sliderH, 3, accent);

        // Desenhar knob
        int knobX = x + progressW - knobSize/2;
        Color knobColor = hovered || isDragging[key] ? Color(255, 255, 255, 255) : Color(220, 220, 220, 255);
        DrawRoundedRect(knobX, sliderY - knobSize/2 + sliderH/2, knobSize, knobSize, knobSize/2, knobColor);

        return isDragging[key];
    }

    // Separador visual
    void DrawSeparator(int x, int y, int w) {
        Color sepColor = Color(60, 60, 70, 255);
        DrawRect(x, y, w, 1, sepColor);
    }

    // Grupo com título
    void DrawGroupPro(int x, int y, int w, int h, const char* title) {
        // Fundo do grupo
        Color groupColor = Color(20, 20, 30, 100);
        DrawRoundedRect(x, y, w, h, 5, groupColor);

        // Borda sutil
        Color borderColor = Color(60, 60, 70, 255);
        DrawRectOutline(x, y, w, h, borderColor, 1);

        // Título do grupo
        DrawMessage(hFontSection, x + 10, y + 8, 255, title);

        // Linha decorativa
        DrawRect(x + 10, y + 28, w - 20, 1, accent);
    }

    void UpdateInput() {
        // Obter posição do mouse
        GetCursorPos(&g_mousePos);
        ScreenToClient(g_hwnd, &g_mousePos);

        // Estado atual do botão esquerdo
        bool currentMouseState = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;

        // Detectar eventos de clique
        g_mousePressed = currentMouseState && !g_lastMouseState;  // Botão foi pressionado agora
        g_mouseReleased = !currentMouseState && g_lastMouseState; // Botão foi solto agora
        g_mouseDown = currentMouseState;                          // Botão está sendo segurado

        // Atualizar estado anterior
        g_lastMouseState = currentMouseState;
    }
}
