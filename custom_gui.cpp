#include "custom_gui.h"
#include <iostream>

namespace CustomGUI {
    // Variáveis globais
    LPDIRECT3DDEVICE9 g_pd3dDevice = nullptr;
    LPD3DXFONT pFontBig = nullptr;
    LPD3DXFONT pFontSmall = nullptr;
    POINT cursorPos = {0, 0};
    
    // Posição do menu
    int menuX = 100;
    int menuY = 100;
    
    // Variáveis de layout
    int spacing = 0;
    int childSpacing = 0;
    int groupX = 0;
    int groupY = 0;
    int holdPrevX = 0;
    int holdPrevY = 0;
    
    // Estados
    bool comboClicked = false;
    bool isDragging = false;
    POINT dragOffset = {0, 0};
    
    // Cores do tema Neverlose
    Color menuBG(28, 28, 33, 255);      // Fundo principal escuro
    Color sideBar(22, 22, 26, 255);     // Sidebar mais escura
    Color accent(82, 123, 251, 255);    // Azul accent
    Color light(60, 63, 70, 255);       // Cinza claro
    Color groupBG(51, 54, 63, 255);     // Fundo dos grupos
    Color textColor(255, 255, 255, 255); // Texto branco
    
    // Estados das páginas
    bool firstPage = true;
    bool secondPage = false;
    bool thirdPage = false;
    
    // Implementação das funções base
    void DrawRect(int x, int y, int w, int h, Color color) {
        x += menuX;
        y += menuY;
        D3DRECT rect = { x, y, x + w, y + h };
        g_pd3dDevice->Clear(1, &rect, D3DCLEAR_TARGET | D3DCLEAR_ZBUFFER, color.ToD3D(), 0, 0);
    }
    
    void DrawMessage(LPD3DXFONT font, int x, int y, int alpha, const char* message) {
        x += menuX;
        y += menuY;
        D3DCOLOR fontColor = D3DCOLOR_ARGB(alpha, textColor.r, textColor.g, textColor.b);
        RECT rct;
        rct.left = x;
        rct.right = 1680;
        rct.top = y;
        rct.bottom = rct.top + 200;
        font->DrawTextA(NULL, message, -1, &rct, 0, fontColor);
    }
    
    int GetTextWidth(const char* text, LPD3DXFONT font) {
        RECT rect = {0, 0, 0, 0};
        font->DrawTextA(NULL, text, -1, &rect, DT_CALCRECT, D3DCOLOR_ARGB(255, 255, 255, 255));
        return rect.right - rect.left;
    }
    
    bool Initialize(LPDIRECT3DDEVICE9 device) {
        g_pd3dDevice = device;
        
        // Criar fontes
        D3DXCreateFont(device, 18, 0, FW_BOLD, 1, FALSE, DEFAULT_CHARSET,
                      OUT_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE,
                      L"Inter", &pFontBig);
        
        D3DXCreateFont(device, 14, 0, FW_NORMAL, 1, FALSE, DEFAULT_CHARSET,
                      OUT_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE,
                      L"Inter", &pFontSmall);
        
        return (pFontBig != nullptr && pFontSmall != nullptr);
    }
    
    void Cleanup() {
        if (pFontBig) {
            pFontBig->Release();
            pFontBig = nullptr;
        }
        if (pFontSmall) {
            pFontSmall->Release();
            pFontSmall = nullptr;
        }
    }
    
    bool IsMouseOver(int x, int y, int w, int h) {
        GetCursorPos(&cursorPos);
        return (cursorPos.x > menuX + x && cursorPos.x < menuX + x + w && 
                cursorPos.y > menuY + y && cursorPos.y < menuY + y + h);
    }
    
    void Sleep(int ms) {
        ::Sleep(ms);
    }
    
    void HandleMenuDrag() {
        GetCursorPos(&cursorPos);
        
        // Área de drag (barra superior do menu)
        if (IsMouseOver(0, -5, 500, 25)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) {
                if (!isDragging) {
                    isDragging = true;
                    dragOffset.x = cursorPos.x - menuX;
                    dragOffset.y = cursorPos.y - menuY;
                }
            }
        }
        
        if (isDragging) {
            if (GetAsyncKeyState(VK_LBUTTON) & 0x8000) {
                menuX = cursorPos.x - dragOffset.x;
                menuY = cursorPos.y - dragOffset.y;
            } else {
                isDragging = false;
            }
        }
    }
    
    void ResetSpacing() {
        spacing = 0;
        childSpacing = 0;
    }

    // Implementação dos elementos da GUI
    bool DrawSideBar(const char* label, bool active, int number) {
        GetCursorPos(&cursorPos);
        Color activeColor(45, 48, 55, 255);
        Color hovered(60, 63, 70, 255);

        int buttonY = 5 + (number * 87);

        if (IsMouseOver(5, buttonY, 90, 85)) {
            DrawRect(5, buttonY, 90, 85, hovered);
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                return true;
            }
        }

        if (active) {
            DrawRect(5, buttonY, 90, 85, activeColor);
        }

        int width = GetTextWidth(label, pFontBig);
        DrawMessage(pFontBig, 50 - width / 2, buttonY + 36, 255, label);

        return false;
    }

    void DrawGroupBox(int width, int height, const char* label, bool sameline) {
        groupX = (sameline ? holdPrevX + 120 + 10 : 120) + 7;
        groupY = sameline ? holdPrevY : childSpacing;

        // Linha superior azul
        DrawRect(sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY - 4 : childSpacing - 4, width, 4, accent);
        // Fundo do grupo
        DrawRect(sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY : childSpacing, width, height, groupBG);

        // Título do grupo
        DrawMessage(pFontSmall, sameline ? holdPrevX + 120 + 10 : 120, sameline ? holdPrevY - 22 : childSpacing - 22, 255, label);

        if (!sameline) {
            childSpacing += height + 25;
            holdPrevY = childSpacing - height - 30 + 4;
            holdPrevX = width;
        }
        spacing = 10;
    }

    void DrawCheckBox(bool& change, const char* label) {
        GetCursorPos(&cursorPos);

        // Desenhar checkbox
        if (change) {
            DrawRect(groupX, groupY + spacing, 15, 15, accent);
        } else {
            DrawRect(groupX, groupY + spacing, 15, 15, light);
        }

        // Verificar clique
        if (IsMouseOver(groupX, groupY + spacing, 15, 15)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                change = !change;
                Sleep(100);
            }
        }

        // Texto do label
        DrawMessage(pFontSmall, groupX + 20, groupY + spacing - 2, 255, label);
        spacing += 25; // Espaçamento maior para melhor visibilidade
    }

    void DrawDropDown(std::vector<const char*> info, int& manage, const char* label, bool& clicked) {
        GetCursorPos(&cursorPos);

        // Caixa principal do dropdown
        DrawRect(groupX, groupY + spacing, 100, 20, light);
        DrawRect(groupX + 1, groupY + 1 + spacing, 98, 18, groupBG);
        DrawMessage(pFontSmall, groupX + 3, groupY + spacing, 255, info[manage]);

        // Verificar clique na caixa principal
        if (IsMouseOver(groupX, groupY + spacing, 100, 20)) {
            if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                clicked = !clicked;
                comboClicked = !comboClicked;
                Sleep(100);
            }
        }

        // Desenhar opções se aberto
        if (clicked) {
            for (int i = 0; i < (int)info.size(); i++) {
                int optionY = groupY + 20 + (i * 20) + spacing;
                DrawRect(groupX, optionY, 100, 20, light);

                if (IsMouseOver(groupX, optionY, 100, 20)) {
                    DrawRect(groupX + 1, optionY, 98, 20, light);
                    if (GetAsyncKeyState(VK_LBUTTON) & 1) {
                        manage = i;
                        clicked = false;
                        comboClicked = false;
                        Sleep(100);
                    }
                } else {
                    DrawRect(groupX + 1, optionY, 98, 20, groupBG);
                }

                if (i == (int)info.size() - 1) {
                    DrawRect(groupX, optionY + 20 + 1, 100, 1, light);
                }
                DrawMessage(pFontSmall, groupX + 3, optionY, 255, info[i]);
            }
        }

        DrawMessage(pFontSmall, groupX + 103, groupY + spacing, 255, label);
        spacing += 30;
    }

    void DrawSlider(const char* label, int& change, int min, int max) {
        GetCursorPos(&cursorPos);

        // Converter valor para string
        std::string value_string = std::to_string(change) + ".0";

        // Verificar se está sendo arrastado
        if (IsMouseOver(groupX, groupY + spacing + 21, 178, 10) && (GetAsyncKeyState(VK_LBUTTON) & 0x8000)) {
            change = (cursorPos.x - menuX - groupX) * (max - min) / 178;
        }

        // Limitar valores
        if (change > max) change = max;
        if (change < min) change = min;

        // Calcular posição do valor
        int valueX = ((change - min) * 178 / (max - min));

        // Desenhar elementos
        DrawMessage(pFontSmall, groupX, groupY + spacing + 2, 255, label);
        DrawMessage(pFontSmall, groupX + 150, groupY + spacing + 2, 255, value_string.c_str());
        DrawRect(groupX, groupY + spacing + 21, 178, 10, sideBar);
        DrawRect(groupX, groupY + spacing + 21, valueX, 10, accent);

        spacing += 35;
    }

    void RenderMenu() {
        // Handle drag do menu
        HandleMenuDrag();

        // Desenhar estrutura base do menu
        DrawRect(0, -5, 500, 5, accent);          // Barra superior
        DrawRect(0, 0, 500, 400, menuBG);         // Fundo principal
        DrawRect(0, 0, 100, 400, sideBar);        // Sidebar

        // Variáveis estáticas para controle
        static bool f = false, s = false, t = false;
        static bool cF = false, cS = false;
        static int testManage = 0;
        static int sliderTest = 50;
        static bool checkTest1 = false;
        static bool checkTest2 = false;
        static bool checkTest3 = false;

        // Desenhar botões da sidebar
        f = DrawSideBar("MAIN", firstPage, 0);
        s = DrawSideBar("VISUALS", secondPage, 1);
        t = DrawSideBar("MISC", thirdPage, 2);

        // Controle de páginas
        if (f) {
            firstPage = true;
            secondPage = false;
            thirdPage = false;
        }
        if (s) {
            firstPage = false;
            secondPage = true;
            thirdPage = false;
        }
        if (t) {
            firstPage = false;
            secondPage = false;
            thirdPage = true;
        }

        // Conteúdo baseado na página selecionada
        if (firstPage) {
            DrawGroupBox(200, 150, "AIMBOT", false);
            DrawCheckBox(checkTest1, "Enable Aimbot");
            DrawDropDown({"Head", "Body", "Legs"}, testManage, "Target", cF);
            if (!cF) DrawCheckBox(checkTest2, "Auto Fire");
            if (!cF) DrawSlider("FOV", sliderTest, 0, 180);

            DrawGroupBox(180, 120, "TRIGGERBOT", true);
            DrawCheckBox(checkTest3, "Enable Trigger");
        }

        // Reset spacing para próximo frame
        ResetSpacing();
    }
}
