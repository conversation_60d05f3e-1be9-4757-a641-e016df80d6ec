#pragma once
#include <vector>
#include <string>
#include <windows.h>

// Framework GUI customizada baseada em Win32 GDI
namespace CustomGUI {
    // Variáveis globais
    extern HDC g_hdc;
    extern HWND g_hwnd;
    extern HFONT hFontBig;
    extern HFONT hFontMedium;
    extern HFONT hFontSmall;
    extern POINT cursorPos;
    
    // Posição do menu (para drag)
    extern int menuX;
    extern int menuY;
    
    // Variáveis de layout
    extern int spacing;
    extern int childSpacing;
    extern int groupX;
    extern int groupY;
    extern int holdPrevX;
    extern int holdPrevY;
    
    // Estados globais
    extern bool comboClicked;
    extern bool isDragging;
    extern POINT dragOffset;
    
    // Cores
    struct Color {
        int r, g, b, a;
        Color(int red, int green, int blue, int alpha = 255) : r(red), g(green), b(blue), a(alpha) {}
        COLORREF ToWin32() const { return RGB(r, g, b); }
    };
    
    // Cores do tema Neverlose (com transparência)
    extern Color menuBG;        // rgba(0, 0, 0, 0.75) - Fundo principal transparente
    extern Color sideBar;       // rgba(14, 12, 16, 0.75) - Sidebar transparente
    extern Color accent;        // #527BFB - Azul accent
    extern Color light;         // rgba(25, 25, 35, 0.5) - Elementos claros
    extern Color groupBG;       // rgba(10, 10, 18, 0.5) - Fundo dos grupos
    extern Color textColor;     // #FFFFFF - Texto branco
    extern Color textSecondary; // rgba(255, 255, 255, 0.5) - Texto secundário
    extern Color textTertiary;  // rgba(255, 255, 255, 0.25) - Texto terciário
    
    // Páginas do menu
    extern bool firstPage;
    extern bool secondPage;
    extern bool thirdPage;
    
    // Funções base de desenho
    void DrawRect(int x, int y, int w, int h, Color color);
    void DrawRectOutline(int x, int y, int w, int h, Color color, int thickness = 1);
    void DrawRoundedRect(int x, int y, int w, int h, int radius, Color color);
    void DrawMessage(HFONT font, int x, int y, int alpha, const char* message);
    int GetTextWidth(const char* text, HFONT font);

    // Funções para ícones SVG
    void DrawAimbotIcon(int x, int y, int size, Color color);
    void DrawEyeIcon(int x, int y, int size, Color color);
    void DrawMouseIcon(int x, int y, int size, Color color);
    void DrawLayerIcon(int x, int y, int size, Color color);
    void DrawListIcon(int x, int y, int size, Color color);
    void DrawSettingsIcon(int x, int y, int size, Color color);
    void DrawSaveIcon(int x, int y, int size, Color color);
    void DrawSearchIcon(int x, int y, int size, Color color);

    // Inicialização
    bool Initialize(HWND hwnd);
    void Cleanup();
    
    // Função principal do menu
    void RenderMenu();
    
    // Elementos da GUI
    bool DrawSideBar(const char* label, bool active, int number);
    bool DrawSideBarWithIcon(const char* text, bool active, int index, void(*iconFunc)(int, int, int, Color));
    void DrawGroupBox(int width, int height, const char* label, bool sameline);
    void DrawCheckBox(bool& change, const char* label);
    void DrawDropDown(std::vector<const char*> info, int& manage, const char* label, bool& clicked);
    void DrawSlider(const char* label, int& change, int min, int max);
    bool DrawButton(int x, int y, int w, int h, const char* text, Color bgColor = Color(0, 0, 0, 0));
    bool DrawIconButton(int x, int y, int w, int h, const char* text, void(*iconFunc)(int, int, int, Color), Color bgColor = Color(0, 0, 0, 0));
    
    // Sistema de drag do menu
    void HandleMenuDrag();
    void ResetSpacing();
    
    // Utilitários
    bool IsMouseOver(int x, int y, int w, int h);
    void Sleep(int ms);
}
