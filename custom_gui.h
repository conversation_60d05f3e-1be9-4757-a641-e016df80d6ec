#pragma once
#include <d3d9.h>
#include <d3dx9.h>
#include <vector>
#include <string>
#include <windows.h>

// Framework GUI customizada baseada em DirectX9
namespace CustomGUI {
    // Variáveis globais
    extern LPDIRECT3DDEVICE9 g_pd3dDevice;
    extern LPD3DXFONT pFontBig;
    extern LPD3DXFONT pFontSmall;
    extern POINT cursorPos;
    
    // Posição do menu (para drag)
    extern int menuX;
    extern int menuY;
    
    // Variáveis de layout
    extern int spacing;
    extern int childSpacing;
    extern int groupX;
    extern int groupY;
    extern int holdPrevX;
    extern int holdPrevY;
    
    // Estados globais
    extern bool comboClicked;
    extern bool isDragging;
    extern POINT dragOffset;
    
    // Cores
    struct Color {
        int r, g, b, a;
        Color(int red, int green, int blue, int alpha = 255) : r(red), g(green), b(blue), a(alpha) {}
        D3DCOLOR ToD3D() const { return D3DCOLOR_ARGB(a, r, g, b); }
    };
    
    // Cores do tema
    extern Color menuBG;
    extern Color sideBar;
    extern Color accent;
    extern Color light;
    extern Color groupBG;
    extern Color textColor;
    
    // Páginas do menu
    extern bool firstPage;
    extern bool secondPage;
    extern bool thirdPage;
    
    // Funções base de desenho
    void DrawRect(int x, int y, int w, int h, Color color);
    void DrawMessage(LPD3DXFONT font, int x, int y, int alpha, const char* message);
    int GetTextWidth(const char* text, LPD3DXFONT font);
    
    // Inicialização
    bool Initialize(LPDIRECT3DDEVICE9 device);
    void Cleanup();
    
    // Função principal do menu
    void RenderMenu();
    
    // Elementos da GUI
    bool DrawSideBar(const char* label, bool active, int number);
    void DrawGroupBox(int width, int height, const char* label, bool sameline);
    void DrawCheckBox(bool& change, const char* label);
    void DrawDropDown(std::vector<const char*> info, int& manage, const char* label, bool& clicked);
    void DrawSlider(const char* label, int& change, int min, int max);
    
    // Sistema de drag do menu
    void HandleMenuDrag();
    void ResetSpacing();
    
    // Utilitários
    bool IsMouseOver(int x, int y, int w, int h);
    void Sleep(int ms);
}
