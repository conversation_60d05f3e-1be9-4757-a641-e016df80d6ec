@echo off
echo Baixando fonte Inter...

:: <PERSON><PERSON>r dire<PERSON> fonts se não existir
if not exist "fonts" mkdir fonts

:: URLs das fontes Inter do Google Fonts
set INTER_REGULAR_URL=https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2
set INTER_MEDIUM_URL=https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hiA.woff2
set INTER_BOLD_URL=https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.woff2

:: Baixar usando PowerShell (disponível no Windows 10+)
echo Baixando Inter Regular...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Regular.ttf' -OutFile 'fonts\Inter-Regular.ttf'}"

echo Baixando Inter Medium...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Medium.ttf' -OutFile 'fonts\Inter-Medium.ttf'}"

echo Baixando Inter Bold...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Bold.ttf' -OutFile 'fonts\Inter-Bold.ttf'}"

echo.
echo Fontes Inter baixadas com sucesso!
echo Arquivos salvos em: fonts\
echo.
pause
