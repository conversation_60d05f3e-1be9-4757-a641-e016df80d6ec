#pragma once
#include "imgui.h"
#include <string>
#include <fstream>
#include <vector>
#include <windows.h>
#include "inter_font_data.h"

namespace FontLoader {
    // Fontes
    static ImFont* MainFont = nullptr;
    static ImFont* TitleFont = nullptr;
    static ImFont* IconFont = nullptr;
    static ImFont* HeaderFont = nullptr;

    // Caminhos para fontes Inter
    static const char* InterRegularPath = "fonts/Inter-Regular.ttf";
    static const char* InterMediumPath = "fonts/Inter-Medium.ttf";
    static const char* InterBoldPath = "fonts/Inter-Bold.ttf";
    
    // Função para verificar se um arquivo existe
    static bool FileExists(const std::string& path) {
        std::ifstream file(path);
        return file.good();
    }
    
    // Criar diretório de fontes se não existir
    static void CreateFontsDirectory() {
        CreateDirectoryA("fonts", NULL);
    }

    // Carregar fontes customizadas
    static void LoadFonts() {
        ImGuiIO& io = ImGui::GetIO();
        CreateFontsDirectory();

        // Configuração otimizada para fontes
        ImFontConfig font_config = ImFontConfig();
        font_config.OversampleH = 2;
        font_config.OversampleV = 2;
        font_config.RasterizerMultiply = 1.0f;
        font_config.PixelSnapH = true;

        // Tentar carregar Inter do diretório local primeiro
        bool interLoaded = false;

        if (FileExists(InterRegularPath)) {
            MainFont = io.Fonts->AddFontFromFileTTF(InterRegularPath, 14.0f, &font_config);
            if (MainFont) interLoaded = true;
        }

        if (FileExists(InterMediumPath)) {
            TitleFont = io.Fonts->AddFontFromFileTTF(InterMediumPath, 16.0f, &font_config);
            HeaderFont = io.Fonts->AddFontFromFileTTF(InterMediumPath, 20.0f, &font_config);
        }

        // Tentar fontes incorporadas se arquivos locais não estiverem disponíveis
        if (!interLoaded && InterFontData::HasEmbeddedFonts()) {
            MainFont = io.Fonts->AddFontFromMemoryTTF(
                (void*)InterFontData::inter_regular_data,
                InterFontData::inter_regular_size,
                14.0f, &font_config
            );
            if (MainFont) interLoaded = true;

            if (InterFontData::inter_medium_data) {
                TitleFont = io.Fonts->AddFontFromMemoryTTF(
                    (void*)InterFontData::inter_medium_data,
                    InterFontData::inter_medium_size,
                    16.0f, &font_config
                );
                HeaderFont = io.Fonts->AddFontFromMemoryTTF(
                    (void*)InterFontData::inter_medium_data,
                    InterFontData::inter_medium_size,
                    20.0f, &font_config
                );
            }
        }

        // Fallback para fontes do sistema se Inter não estiver disponível
        if (!interLoaded) {
            // Tentar Segoe UI Variable (Windows 11)
            if (FileExists("C:\\Windows\\Fonts\\SegoeUIVariable.ttf")) {
                MainFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\SegoeUIVariable.ttf", 14.0f, &font_config);
                TitleFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\SegoeUIVariable.ttf", 16.0f, &font_config);
                HeaderFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\SegoeUIVariable.ttf", 20.0f, &font_config);
            }
            // Fallback para Segoe UI padrão
            else if (FileExists("C:\\Windows\\Fonts\\segoeui.ttf")) {
                MainFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", 14.0f, &font_config);
                TitleFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", 16.0f, &font_config);
                HeaderFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", 20.0f, &font_config);
            }
        }

        // Garantir que pelo menos as fontes padrão sejam carregadas
        if (!MainFont) MainFont = io.Fonts->AddFontDefault();
        if (!TitleFont) TitleFont = MainFont;
        if (!HeaderFont) HeaderFont = TitleFont;

        // Fonte para ícones usando Segoe Fluent Icons se disponível
        if (FileExists("C:\\Windows\\Fonts\\SegoeFluentIcons.ttf")) {
            IconFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\SegoeFluentIcons.ttf", 16.0f, &font_config);
        } else if (FileExists("C:\\Windows\\Fonts\\seguisym.ttf")) {
            IconFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\seguisym.ttf", 16.0f, &font_config);
        }
        if (!IconFont) IconFont = MainFont;

        // Construir atlas de fontes
        io.Fonts->Build();
    }
    
    // Aplicar fonte principal
    static void PushMainFont() {
        if (MainFont) ImGui::PushFont(MainFont);
    }
    
    static void PopMainFont() {
        if (MainFont) ImGui::PopFont();
    }
    
    // Aplicar fonte de título
    static void PushTitleFont() {
        if (TitleFont) ImGui::PushFont(TitleFont);
    }
    
    static void PopTitleFont() {
        if (TitleFont) ImGui::PopFont();
    }
    
    // Aplicar fonte de ícones
    static void PushIconFont() {
        if (IconFont) ImGui::PushFont(IconFont);
    }

    static void PopIconFont() {
        if (IconFont) ImGui::PopFont();
    }

    // Aplicar fonte de cabeçalho
    static void PushHeaderFont() {
        if (HeaderFont) ImGui::PushFont(HeaderFont);
    }

    static void PopHeaderFont() {
        if (HeaderFont) ImGui::PopFont();
    }

    // Verificar se Inter foi carregada
    static bool IsInterLoaded() {
        return FileExists(InterRegularPath) || FileExists(InterMediumPath);
    }
}
