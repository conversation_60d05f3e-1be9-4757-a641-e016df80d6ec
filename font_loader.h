#pragma once
#include "imgui.h"
#include <string>
#include <fstream>
#include <vector>

namespace FontLoader {
    // Fontes
    static ImFont* MainFont = nullptr;
    static ImFont* TitleFont = nullptr;
    static ImFont* IconFont = nullptr;
    
    // Caminho para baixar a fonte Inter
    static const char* InterFontUrl = "https://fonts.google.com/download?family=Inter";
    
    // Função para verificar se um arquivo existe
    static bool FileExists(const std::string& path) {
        std::ifstream file(path);
        return file.good();
    }
    
    // Carregar fontes customizadas
    static void LoadFonts() {
        ImGuiIO& io = ImGui::GetIO();
        
        // Configurar fonte principal (Inter - minimalista)
        ImFontConfig font_config;
        font_config.OversampleH = 3;
        font_config.OversampleV = 3;
        font_config.RasterizerMultiply = 1.2f;
        
        // Tentar carregar a fonte Inter se disponível
        std::string interRegularPath = "C:\\Windows\\Fonts\\Inter-Regular.ttf";
        std::string interMediumPath = "C:\\Windows\\Fonts\\Inter-Medium.ttf";
        
        // Verificar se a fonte Inter está instalada
        bool hasInterFont = FileExists(interRegularPath) || FileExists(interMediumPath);
        
        if (hasInterFont) {
            // Usar Inter se disponível
            std::string fontPath = FileExists(interMediumPath) ? interMediumPath : interRegularPath;
            MainFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 14.0f, &font_config);
            TitleFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 18.0f, &font_config);
        } else {
            // Fallback para Segoe UI (mais minimalista que a padrão)
            MainFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", 14.0f, &font_config);
            TitleFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\segoeui.ttf", 18.0f, &font_config);
        }
        
        // Se ainda não conseguiu carregar, usar fonte padrão
        if (!MainFont) MainFont = io.Fonts->AddFontDefault(&font_config);
        if (!TitleFont) TitleFont = io.Fonts->AddFontDefault();
        
        // Fonte para ícones (16px) - usando Segoe UI Symbol
        IconFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\seguisym.ttf", 16.0f, &font_config);
        if (!IconFont) IconFont = MainFont;
        
        // Construir atlas de fontes
        io.Fonts->Build();
    }
    
    // Aplicar fonte principal
    static void PushMainFont() {
        if (MainFont) ImGui::PushFont(MainFont);
    }
    
    static void PopMainFont() {
        if (MainFont) ImGui::PopFont();
    }
    
    // Aplicar fonte de título
    static void PushTitleFont() {
        if (TitleFont) ImGui::PushFont(TitleFont);
    }
    
    static void PopTitleFont() {
        if (TitleFont) ImGui::PopFont();
    }
    
    // Aplicar fonte de ícones
    static void PushIconFont() {
        if (IconFont) ImGui::PushFont(IconFont);
    }
    
    static void PopIconFont() {
        if (IconFont) ImGui::PopFont();
    }
}
