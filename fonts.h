#pragma once
#include "imgui.h"

namespace Fonts {
    // Fontes customizadas
    static ImFont* MainFont = nullptr;
    static ImFont* TitleFont = nullptr;
    static ImFont* IconFont = nullptr;
    
    // Dados da fonte Roboto (embedded)
    static const unsigned char RobotoRegular[] = {
        // Aqui iria os dados da fonte, mas vou usar uma abordagem mais simples
    };
    
    // Ícones SVG como texto Unicode
    namespace Icons {
        static const char* Aimbot = u8"\uE001";      // 
        static const char* Eye = u8"\uE002";         // 👁
        static const char* Settings = u8"\uE003";    // ⚙
        static const char* Config = u8"\uE004";      // 📁
        static const char* Target = u8"\uE005";      // 🎯
        static const char* Shield = u8"\uE006";      // 🛡
        static const char* Speed = u8"\uE007";       // ⚡
        static const char* Radar = u8"\uE008";       // 📡
        static const char* Save = u8"\uE009";        // 💾
        static const char* Load = u8"\uE010";        // 📂
        static const char* Power = u8"\uE011";       // ⚡
        static const char* Check = u8"\uE012";       // ✓
        static const char* Cross = u8"\uE013";       // ✗
    }
    
    // Inicializar fontes
    static void LoadFonts() {
        ImGuiIO& io = ImGui::GetIO();
        
        // Fonte principal (Roboto-like)
        MainFont = io.Fonts->AddFontFromMemoryCompressedTTF(
            nullptr, 0, 16.0f, nullptr, io.Fonts->GetGlyphRangesDefault()
        );
        
        // Fonte para títulos (maior)
        TitleFont = io.Fonts->AddFontFromMemoryCompressedTTF(
            nullptr, 0, 24.0f, nullptr, io.Fonts->GetGlyphRangesDefault()
        );
        
        // Fonte para ícones
        IconFont = io.Fonts->AddFontFromMemoryCompressedTTF(
            nullptr, 0, 20.0f, nullptr, io.Fonts->GetGlyphRangesDefault()
        );
        
        // Se não conseguir carregar fontes customizadas, usar padrão
        if (!MainFont) MainFont = io.Fonts->AddFontDefault();
        if (!TitleFont) TitleFont = io.Fonts->AddFontDefault();
        if (!IconFont) IconFont = io.Fonts->AddFontDefault();
        
        io.Fonts->Build();
    }
}
