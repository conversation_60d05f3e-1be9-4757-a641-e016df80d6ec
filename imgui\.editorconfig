# See http://editorconfig.org to read about the EditorConfig format.
# - In theory automatically supported by VS2017+ and most common IDE or text editors.
# - In practice VS2019-VS2022 stills don't trim trailing whitespaces correctly :(
#   - Suggest installing this to trim whitespaces:
#      GitHub https://github.com/madskristensen/TrailingWhitespace
#      VS2019 https://marketplace.visualstudio.com/items?itemName=MadsKristensen.TrailingWhitespaceVisualizer
#      VS2022 https://marketplace.visualstudio.com/items?itemName=MadsKristensen.TrailingWhitespace64
#     (in spite of its name doesn't only visualize but also trims)
#   - Alternative for older VS2010 to VS2015: https://marketplace.visualstudio.com/items?itemName=EditorConfigTeam.EditorConfig

# top-most EditorConfig file
root = true

# Default settings:
# Use 4 spaces as indentation
[*]
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true

[imstb_*]
indent_size = 3
trim_trailing_whitespace = false

[Makefile]
indent_style = tab
indent_size = 4
