#pragma once

// Dados da fonte Inter incorporados
// Para usar fontes incorporadas, descomente as linhas abaixo e adicione os dados da fonte

namespace InterFontData {
    // Placeholder para dados da fonte Inter
    // Em um projeto real, você incluiria os dados binários da fonte aqui
    // Por exemplo: static const unsigned char inter_regular_data[] = { ... };
    
    static const unsigned char* inter_regular_data = nullptr;
    static const int inter_regular_size = 0;
    
    static const unsigned char* inter_medium_data = nullptr;
    static const int inter_medium_size = 0;
    
    static const unsigned char* inter_bold_data = nullptr;
    static const int inter_bold_size = 0;
    
    // Função para verificar se os dados estão disponíveis
    static bool HasEmbeddedFonts() {
        return inter_regular_data != nullptr && inter_regular_size > 0;
    }
}
