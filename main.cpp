// File: main.cpp
// CS2 Cheat Menu - Modern Animated Overlay
// DirectX9 + ImGui with custom styling and animations
// Visual Studio 2022 Project

#include <d3d9.h>
#include <tchar.h>
#define NOMINMAX
#include <windows.h>
#include "imgui.h"
#include "imgui_impl_dx9.h"
#include "imgui_impl_win32.h"
#include "menu_style.h"
#include "animations.h"
#include "menu_components.h"
#include "modern_ui.h"
#include "font_loader.h"
#include "notifications.h"

#pragma comment(lib, "d3d9.lib")

// Global variables
static LPDIRECT3D9              g_pD3D = NULL;
static LPDIRECT3DDEVICE9        g_pd3dDevice = NULL;
static D3DPRESENT_PARAMETERS    g_d3dpp = {};
static HWND                     g_hWnd = NULL;
static bool                     g_ShowMenu = true;

// Prototypes
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
void RenderMenu();

// Função para criar o dispositivo Direct3D
bool CreateDeviceD3D(HWND hWnd) {
    if ((g_pD3D = Direct3DCreate9(D3D_SDK_VERSION)) == NULL)
        return false;
    ZeroMemory(&g_d3dpp, sizeof(g_d3dpp));
    g_d3dpp.Windowed = TRUE;
    g_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
    g_d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;
    g_d3dpp.EnableAutoDepthStencil = TRUE;
    g_d3dpp.AutoDepthStencilFormat = D3DFMT_D16;
    g_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_ONE;
    g_d3dpp.BackBufferWidth = GetSystemMetrics(SM_CXSCREEN);
    g_d3dpp.BackBufferHeight = GetSystemMetrics(SM_CYSCREEN);
    if (g_pD3D->CreateDevice(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, hWnd,
        D3DCREATE_HARDWARE_VERTEXPROCESSING, &g_d3dpp, &g_pd3dDevice) < 0)
        return false;
    return true;
}

void CleanupDeviceD3D() {
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
    if (g_pD3D) { g_pD3D->Release(); g_pD3D = NULL; }
}

void ResetDevice() {
    ImGui_ImplDX9_InvalidateDeviceObjects();
    g_pd3dDevice->Reset(&g_d3dpp);
    ImGui_ImplDX9_CreateDeviceObjects();
}

// Função principal
int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int) {
    // Registrar classe da janela
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(NULL), NULL, NULL, NULL, NULL, _T("CS2CheatMenu"), NULL };
    RegisterClassEx(&wc);
    
    // Criar janela overlay transparente sem bordas
    g_hWnd = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TOOLWINDOW,  // Sempre no topo, layered, sem taskbar
        wc.lpszClassName, 
        _T("CS2 CheatMenu Overlay"), 
        WS_POPUP,  // Sem bordas nem barra de título
        0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN),  // Tela cheia
        NULL, NULL, wc.hInstance, NULL
    );
    
    // Configurar transparência da janela - chroma key no preto para transparência real
    SetLayeredWindowAttributes(g_hWnd, RGB(0, 0, 0), 0, LWA_COLORKEY);

    // Inicializar Direct3D
    if (!CreateDeviceD3D(g_hWnd)) {
        CleanupDeviceD3D();
        UnregisterClass(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    ShowWindow(g_hWnd, SW_SHOWDEFAULT);
    UpdateWindow(g_hWnd);

    // Inicializar ImGui
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    
    // Carregar fontes customizadas
    FontLoader::LoadFonts();
    
    // Aplicar estilo moderno
    MenuStyle::SetupStyle();
    
    ImGui_ImplWin32_Init(g_hWnd);
    ImGui_ImplDX9_Init(g_pd3dDevice);

    // Inicializar posição do menu no centro da tela
    ImVec2 screenCenter = ImVec2(GetSystemMetrics(SM_CXSCREEN) * 0.5f, GetSystemMetrics(SM_CYSCREEN) * 0.5f);
    Animations::Menu::SetPosition(screenCenter);
    
    // Loop principal
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }
        // Toggle menu com INSERT
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            Animations::Menu::SetVisible(g_ShowMenu);
            
            // Configurar transparência da janela baseado na visibilidade do menu
            if (g_ShowMenu) {
                // Menu visível - permitir interação
                SetWindowLong(g_hWnd, GWL_EXSTYLE, GetWindowLong(g_hWnd, GWL_EXSTYLE) & ~WS_EX_TRANSPARENT);
            } else {
                // Menu oculto - mouse passa através
                SetWindowLong(g_hWnd, GWL_EXSTYLE, GetWindowLong(g_hWnd, GWL_EXSTYLE) | WS_EX_TRANSPARENT);
            }
        }
        
        // Fechar aplicação com ESC
        if (GetAsyncKeyState(VK_ESCAPE) & 1) {
            PostQuitMessage(0);
        }

        // Atualizar sistema de animações
        Animations::Update();
        Animations::Menu::Update();

        // Iniciar frame ImGui
        ImGui_ImplDX9_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Renderizar menu com animação
        if (Animations::Menu::ShouldRender()) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, Animations::Menu::GetAlpha());
            RenderMenu();
            ImGui::PopStyleVar();
        }

        ImGui::EndFrame();
        
        // Configurar estados de renderização para transparência
        g_pd3dDevice->SetRenderState(D3DRS_ZENABLE, FALSE);
        g_pd3dDevice->SetRenderState(D3DRS_ALPHABLENDENABLE, TRUE);
        g_pd3dDevice->SetRenderState(D3DRS_SRCBLEND, D3DBLEND_SRCALPHA);
        g_pd3dDevice->SetRenderState(D3DRS_DESTBLEND, D3DBLEND_INVSRCALPHA);
        g_pd3dDevice->SetRenderState(D3DRS_SCISSORTESTENABLE, FALSE);
        
        // Limpar com fundo completamente transparente
        D3DCOLOR clear_col_dx = D3DCOLOR_RGBA(0, 0, 0, 0);
        g_pd3dDevice->Clear(0, NULL, D3DCLEAR_TARGET | D3DCLEAR_ZBUFFER, clear_col_dx, 1.0f, 0);
        if (g_pd3dDevice->BeginScene() >= 0) {
            ImGui::Render();
            ImGui_ImplDX9_RenderDrawData(ImGui::GetDrawData());
            g_pd3dDevice->EndScene();
        }
        HRESULT result = g_pd3dDevice->Present(NULL, NULL, NULL, NULL);
        if (result == D3DERR_DEVICELOST && g_pd3dDevice->TestCooperativeLevel() == D3DERR_DEVICENOTRESET)
            ResetDevice();
    }

    // Limpeza
    ImGui_ImplDX9_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
    CleanupDeviceD3D();
    DestroyWindow(g_hWnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);
    return 0;
}

// Modern CS2 Cheat Menu - Professional Sidebar Design
void RenderMenu() {
    // Configurações da janela principal
    ImGuiWindowFlags window_flags = 
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoCollapse |
        ImGuiWindowFlags_NoScrollbar;
    
    // Permitir movimentação do menu
    if (Animations::Menu::IsDragging()) {
        window_flags &= ~ImGuiWindowFlags_NoMove;
    } else {
        window_flags |= ImGuiWindowFlags_NoMove;
    }
    
    // Posicionar o menu usando a posição armazenada
    ImVec2 menuPos = Animations::Menu::GetPosition();
    ImGui::SetNextWindowPos(menuPos, ImGuiCond_Once, ImVec2(0.5f, 0.5f));
    ImGui::SetNextWindowSize(ImVec2(800, 550), ImGuiCond_Always); // Tamanho mais compacto
    
    // Estilo moderno minimalista
    ImGui::PushStyleColor(ImGuiCol_WindowBg, ModernUI::Colors::Background);
    ImGui::PushStyleColor(ImGuiCol_Border, ModernUI::Colors::Border);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, ModernUI::Layout::Rounding);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, ModernUI::Layout::BorderWidth);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0, 0));
    
    if (!ImGui::Begin("##CS2CheatMenu", &g_ShowMenu, window_flags)) {
        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(2);
        ImGui::End();
        return;
    }
    
    // Sistema de movimento melhorado
    static bool isDragging = false;
    static ImVec2 dragOffset = ImVec2(0, 0);

    ImVec2 mousePos = ImGui::GetMousePos();
    ImVec2 windowPos = ImGui::GetWindowPos();
    ImVec2 windowSize = ImGui::GetWindowSize();

    // Área da barra de título para arrastar (30px de altura)
    bool isHoveringTitleBar = ImGui::IsMouseHoveringRect(
        windowPos,
        ImVec2(windowPos.x + windowSize.x, windowPos.y + 30)
    );

    // Iniciar drag
    if (isHoveringTitleBar && ImGui::IsMouseClicked(0)) {
        isDragging = true;
        dragOffset = ImVec2(mousePos.x - windowPos.x, mousePos.y - windowPos.y);
        Animations::Menu::StartDragging();
    }

    // Parar drag
    if (!ImGui::IsMouseDown(0) && isDragging) {
        isDragging = false;
        Animations::Menu::StopDragging();
    }

    // Aplicar movimento durante drag
    if (isDragging && ImGui::IsMouseDragging(0)) {
        ImVec2 newPos = ImVec2(mousePos.x - dragOffset.x, mousePos.y - dragOffset.y);

        // Limitar posição dentro da tela
        ImVec2 displaySize = ImGui::GetIO().DisplaySize;
        newPos.x = (newPos.x < 0.0f) ? 0.0f : ((newPos.x > displaySize.x - windowSize.x) ? displaySize.x - windowSize.x : newPos.x);
        newPos.y = (newPos.y < 0.0f) ? 0.0f : ((newPos.y > displaySize.y - windowSize.y) ? displaySize.y - windowSize.y : newPos.y);

        ImGui::SetWindowPos(newPos);
        Animations::Menu::SetPosition(ImVec2(newPos.x + windowSize.x * 0.5f, newPos.y + windowSize.y * 0.5f));
    }

    // Barra de título personalizada para arrastar
    ImDrawList* draw_list = ImGui::GetWindowDrawList();
    ImVec2 window_pos = ImGui::GetWindowPos(); // Atualizar após possível movimento
    ImVec2 window_size = ImGui::GetWindowSize();

    // Cor da barra de título com feedback visual
    ImU32 titleBarColor = IM_COL32(30, 30, 40, 255);
    if (isHoveringTitleBar) {
        titleBarColor = IM_COL32(40, 40, 50, 255); // Mais claro quando hover
        ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
    }
    if (isDragging) {
        titleBarColor = IM_COL32(50, 50, 60, 255); // Ainda mais claro quando arrastando
    }

    // Desenhar barra de título
    draw_list->AddRectFilled(
        window_pos,
        ImVec2(window_pos.x + window_size.x, window_pos.y + 30),
        titleBarColor,
        ModernUI::Layout::Rounding, ImDrawFlags_RoundCornersTop
    );

    // Linha sutil na parte inferior da barra de título
    draw_list->AddLine(
        ImVec2(window_pos.x, window_pos.y + 30),
        ImVec2(window_pos.x + window_size.x, window_pos.y + 30),
        IM_COL32(60, 60, 70, 255),
        1.0f
    );
    
    // Texto do título
    FontLoader::PushTitleFont();
    ImVec2 title_pos = ImVec2(window_pos.x + 15, window_pos.y + 5);
    draw_list->AddText(title_pos, IM_COL32(255, 255, 255, 255), "CS2 CHEAT MENU");
    FontLoader::PopTitleFont();

    // Indicador de movimento (três pontos no canto direito)
    if (isHoveringTitleBar || isDragging) {
        float dotSize = 2.0f;
        float spacing = 6.0f;
        ImVec2 dotPos = ImVec2(window_pos.x + window_size.x - 25, window_pos.y + 15);

        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 2; j++) {
                draw_list->AddCircleFilled(
                    ImVec2(dotPos.x + i * spacing, dotPos.y + j * spacing - 3),
                    dotSize,
                    IM_COL32(150, 150, 150, 200)
                );
            }
        }
    }
    
    // Definir itens da sidebar
    static ModernUI::SidebarItem sidebar_items[] = {
        {ModernUI::Icons::Target, "Aimbot", 0, false},
        {ModernUI::Icons::Eye, "Visuals", 1, false},
        {ModernUI::Icons::Speed, "Movement", 2, false},
        {ModernUI::Icons::Misc, "Misc", 3, false},
        {ModernUI::Icons::Cog, "Settings", 4, false},
        {ModernUI::Icons::Folder, "Configs", 5, false}
    };
    
    static int selected_tab = 0;
    
    // Renderizar sidebar e obter seleção
    int new_selection = ModernUI::RenderSidebar(sidebar_items, 6, selected_tab);
    if (new_selection != selected_tab) {
        selected_tab = new_selection;
        // Adicionar efeito sonoro ou visual de feedback
        Animations::SetTarget("tab_change", 1.0f, 12.0f, 3); // Efeito bounce
    }
    
    // Área de conteúdo principal
    ImGui::SetCursorPos(ImVec2(ModernUI::Layout::SidebarWidth + ModernUI::Layout::Padding, 30 + ModernUI::Layout::Padding));
    
    // Calcular tamanho da área de conteúdo
    ImVec2 content_size = ImVec2(
        800 - ModernUI::Layout::SidebarWidth - ModernUI::Layout::Padding * 2,
        550 - 30 - ModernUI::Layout::Padding * 2
    );
    
    // Aplicar fonte principal para o conteúdo
    FontLoader::PushMainFont();
    ImGui::BeginChild("##content", content_size, false, ImGuiWindowFlags_NoScrollbar);
    
    // Tabs principais com animação
    static int selectedTab = 0;
    const char* tabLabels[] = { "AIMBOT", "VISUALS", "MISC", "CONFIG" };
    
    // Renderizar tabs com animação
    ImGui::BeginGroup();
    for (int i = 0; i < 4; i++) {
        if (i > 0) ImGui::SameLine(0, 5);
        if (Animations::Elements::AnimatedTab(tabLabels[i], selectedTab == i, ImVec2(content_size.x / 4 - 5, 35))) {
            selectedTab = i;
        }
    }
    ImGui::EndGroup();
    
    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Spacing();
    
    // Renderizar conteúdo baseado na aba selecionada
    switch (selected_tab) {
        case 0: // AIMBOT
        {
            // Header da seção com fonte de título
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Aimbot Configuration", ModernUI::Icons::Target);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            // Card de configurações principais
            ModernUI::BeginCard("Main Settings");
            {
                static bool aimbot_enabled = false;
                static float aimbot_fov = 90.0f;
                static float aimbot_smooth = 5.0f;
                
                ModernUI::ModernToggle("Enable Aimbot", &aimbot_enabled);
                ModernUI::StatusIndicator("Status", aimbot_enabled);
                
                ImGui::Spacing();
                
                ModernUI::ModernSlider("FOV", &aimbot_fov, 10.0f, 180.0f, "%.0f°");
                ModernUI::ModernSlider("Smoothing", &aimbot_smooth, 1.0f, 20.0f, "%.1f");
            }
            ModernUI::EndCard();
            
            ImGui::Spacing();
            
            // Card de trigger bot
            ModernUI::BeginCard("Trigger Bot");
            {
                static bool triggerbot = false;
                static float trigger_delay = 50.0f;
                
                ModernUI::ModernToggle("Enable Trigger Bot", &triggerbot);
                if (triggerbot) {
                    ModernUI::ModernSlider("Delay", &trigger_delay, 0.0f, 200.0f, "%.0f ms");
                }
            }
            ModernUI::EndCard();
            
            break;
        }
        
        case 1: // VISUALS
        {
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Visual Enhancements", ModernUI::Icons::Eye);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            ModernUI::BeginCard("ESP Settings");
            {
                static bool esp_enabled = false;
                static bool esp_box = true;
                static bool esp_name = true;
                static bool esp_health = true;
                
                ModernUI::ModernToggle("Enable ESP", &esp_enabled);
                
                if (esp_enabled) {
                    ImGui::Spacing();
                    ModernUI::ModernToggle("Box ESP", &esp_box);
                    ModernUI::ModernToggle("Name ESP", &esp_name);
                    ModernUI::ModernToggle("Health ESP", &esp_health);
                }
            }
            ModernUI::EndCard();
            
            ImGui::Spacing();
            
            ModernUI::BeginCard("World Modifications");
            {
                static bool no_flash = false;
                static bool no_smoke = false;
                static float brightness = 1.0f;
                
                ModernUI::ModernToggle("No Flash", &no_flash);
                ModernUI::ModernToggle("No Smoke", &no_smoke);
                ModernUI::ModernSlider("Brightness", &brightness, 0.5f, 3.0f, "%.1f");
            }
            ModernUI::EndCard();
            
            break;
        }
        
        case 2: // MOVEMENT
        {
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Movement Enhancement", ModernUI::Icons::Speed);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            ModernUI::BeginCard("Movement Hacks");
            {
                static bool bhop = false;
                static bool autostrafe = false;
                static bool speedhack = false;
                static float speed_factor = 1.0f;
                
                ModernUI::ModernToggle("Bunny Hop", &bhop);
                ModernUI::ModernToggle("Auto Strafe", &autostrafe);
                ModernUI::ModernToggle("Speed Hack", &speedhack);
                
                if (speedhack) {
                    ModernUI::ModernSlider("Speed Factor", &speed_factor, 1.0f, 5.0f, "%.1fx");
                }
            }
            ModernUI::EndCard();
            
            break;
        }
        
        case 3: // MISC
        {
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Miscellaneous", ModernUI::Icons::Misc);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            ModernUI::BeginCard("Utilities");
            {
                static bool radar_hack = false;
                static bool rank_reveal = false;
                
                ModernUI::ModernToggle("Radar Hack", &radar_hack);
                ModernUI::ModernToggle("Rank Reveal", &rank_reveal);
                
                ImGui::Spacing();
                
                if (ModernUI::ModernButton("Force Update", ImVec2(120, 32))) {
                    // Implementar force update
                }
            }
            ModernUI::EndCard();
            
            break;
        }
        
        case 4: // SETTINGS
        {
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Application Settings", ModernUI::Icons::Cog);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            ModernUI::BeginCard("Interface");
            {
                static bool show_fps = true;
                static bool minimize_to_tray = false;
                static float ui_scale = 1.0f;
                
                ModernUI::ModernToggle("Show FPS", &show_fps);
                ModernUI::ModernToggle("Minimize to Tray", &minimize_to_tray);
                ModernUI::ModernSlider("UI Scale", &ui_scale, 0.8f, 1.5f, "%.1fx");
            }
            ModernUI::EndCard();
            
            break;
        }
        
        case 5: // CONFIGS
        {
            FontLoader::PushTitleFont();
            ImGui::PushStyleColor(ImGuiCol_Text, ModernUI::Colors::Primary);
            ImGui::Text("%s  Configuration Manager", ModernUI::Icons::Folder);
            ImGui::PopStyleColor();
            FontLoader::PopTitleFont();
            
            ImGui::Separator();
            ImGui::Spacing();
            
            ModernUI::BeginCard("Config Management");
            {
                static char configName[64] = "default";
                
                ImGui::InputText("Config Name", configName, sizeof(configName));
                ImGui::Spacing();
                
                if (ModernUI::ModernButton("Save Config", ImVec2(100, 32))) {
                    // Implementar save
                }
                ImGui::SameLine();
                if (ModernUI::ModernButton("Load Config", ImVec2(100, 32))) {
                    // Implementar load
                }
                
                ImGui::Spacing();
                ImGui::Text("Saved Configurations:");
                
                // Lista de configs
                const char* configs[] = { "default.cfg", "legit.cfg", "rage.cfg" };
                for (int i = 0; i < 3; i++) {
                    if (ImGui::Selectable(configs[i])) {
                        // Carregar config
                    }
                }
            }
            ModernUI::EndCard();
            
            break;
        }
    }
    
    ImGui::EndChild(); // End content area
    FontLoader::PopMainFont(); // Pop main font
    
    // Footer com informações
    draw_list = ImGui::GetWindowDrawList();
    ImVec2 footer_window_pos = ImGui::GetWindowPos();
    ImVec2 footer_window_size = ImGui::GetWindowSize();

    // Linha separadora do footer
    ImVec2 footer_line_start = ImVec2(footer_window_pos.x + ModernUI::Layout::SidebarWidth, footer_window_pos.y + footer_window_size.y - 40);
    ImVec2 footer_line_end = ImVec2(footer_window_pos.x + footer_window_size.x, footer_window_pos.y + footer_window_size.y - 40);
    draw_list->AddLine(footer_line_start, footer_line_end, ModernUI::Colors::Border, 1.0f);

    // Status e controles no footer
    ImVec2 status_pos = ImVec2(footer_window_pos.x + ModernUI::Layout::SidebarWidth + ModernUI::Layout::Padding, footer_window_pos.y + footer_window_size.y - 30);
    draw_list->AddText(status_pos, ModernUI::Colors::Success, "● ONLINE");

    ImVec2 controls_pos = ImVec2(footer_window_pos.x + footer_window_size.x - 300, footer_window_pos.y + footer_window_size.y - 30);
    draw_list->AddText(controls_pos, ModernUI::Colors::TextMuted, "INSERT: Toggle | ESC: Exit | DRAG: Move Menu");
    
    ImGui::End();
    ImGui::PopStyleVar(3);
    ImGui::PopStyleColor(2);
}

// WndProc para ImGui
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    // Comentado temporariamente para resolver erro de compilação
    // if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
    //     return true;
    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            g_d3dpp.BackBufferWidth = LOWORD(lParam);
            g_d3dpp.BackBufferHeight = HIWORD(lParam);
            ResetDevice();
        }
        return 0;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}