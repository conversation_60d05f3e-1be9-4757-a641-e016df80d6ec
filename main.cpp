// File: main.cpp
// CS2 Cheat Menu - Modern Animated Overlay
// DirectX9 + ImGui with custom styling and animations
// Visual Studio 2022 Project

#include <d3d9.h>
#include <tchar.h>
#define NOMINMAX
#include <windows.h>
#include "imgui.h"
#include "imgui_impl_dx9.h"
#include "imgui_impl_win32.h"
#include "menu_style.h"
#include "animations.h"
#include "menu_components.h"
#include "modern_ui.h"
#include "font_loader.h"
#include "notifications.h"
#include "neverlose_ui.h"

#pragma comment(lib, "d3d9.lib")

// Global variables
static LPDIRECT3D9              g_pD3D = NULL;
static LPDIRECT3DDEVICE9        g_pd3dDevice = NULL;
static D3DPRESENT_PARAMETERS    g_d3dpp = {};
static HWND                     g_hWnd = NULL;
static bool                     g_ShowMenu = true;

// Prototypes
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
void RenderMenu();

// Função para criar o dispositivo Direct3D
bool CreateDeviceD3D(HWND hWnd) {
    if ((g_pD3D = Direct3DCreate9(D3D_SDK_VERSION)) == NULL)
        return false;
    ZeroMemory(&g_d3dpp, sizeof(g_d3dpp));
    g_d3dpp.Windowed = TRUE;
    g_d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
    g_d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;
    g_d3dpp.EnableAutoDepthStencil = TRUE;
    g_d3dpp.AutoDepthStencilFormat = D3DFMT_D16;
    g_d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_ONE;
    g_d3dpp.BackBufferWidth = GetSystemMetrics(SM_CXSCREEN);
    g_d3dpp.BackBufferHeight = GetSystemMetrics(SM_CYSCREEN);
    if (g_pD3D->CreateDevice(D3DADAPTER_DEFAULT, D3DDEVTYPE_HAL, hWnd,
        D3DCREATE_HARDWARE_VERTEXPROCESSING, &g_d3dpp, &g_pd3dDevice) < 0)
        return false;
    return true;
}

void CleanupDeviceD3D() {
    if (g_pd3dDevice) { g_pd3dDevice->Release(); g_pd3dDevice = NULL; }
    if (g_pD3D) { g_pD3D->Release(); g_pD3D = NULL; }
}

void ResetDevice() {
    ImGui_ImplDX9_InvalidateDeviceObjects();
    g_pd3dDevice->Reset(&g_d3dpp);
    ImGui_ImplDX9_CreateDeviceObjects();
}

// Função principal
int APIENTRY WinMain(HINSTANCE hInstance, HINSTANCE, LPSTR, int) {
    // Registrar classe da janela
    WNDCLASSEX wc = { sizeof(WNDCLASSEX), CS_CLASSDC, WndProc, 0L, 0L, GetModuleHandle(NULL), NULL, NULL, NULL, NULL, _T("CS2CheatMenu"), NULL };
    RegisterClassEx(&wc);
    
    // Criar janela overlay transparente sem bordas
    g_hWnd = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TOOLWINDOW,  // Sempre no topo, layered, sem taskbar
        wc.lpszClassName, 
        _T("CS2 CheatMenu Overlay"), 
        WS_POPUP,  // Sem bordas nem barra de título
        0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN),  // Tela cheia
        NULL, NULL, wc.hInstance, NULL
    );
    
    // Configurar transparência da janela - chroma key no preto para transparência real
    SetLayeredWindowAttributes(g_hWnd, RGB(0, 0, 0), 0, LWA_COLORKEY);

    // Inicializar Direct3D
    if (!CreateDeviceD3D(g_hWnd)) {
        CleanupDeviceD3D();
        UnregisterClass(wc.lpszClassName, wc.hInstance);
        return 1;
    }

    ShowWindow(g_hWnd, SW_SHOWDEFAULT);
    UpdateWindow(g_hWnd);

    // Inicializar ImGui
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    
    // Carregar fontes customizadas
    FontLoader::LoadFonts();
    
    // Aplicar estilo moderno
    MenuStyle::SetupStyle();
    
    ImGui_ImplWin32_Init(g_hWnd);
    ImGui_ImplDX9_Init(g_pd3dDevice);

    // Inicializar sistema de animações
    Animations::Menu::Initialize();

    // Inicializar posição do menu no centro da tela
    ImVec2 screenCenter = ImVec2(GetSystemMetrics(SM_CXSCREEN) * 0.5f, GetSystemMetrics(SM_CYSCREEN) * 0.5f);
    Animations::Menu::SetPosition(screenCenter);
    
    // Loop principal
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }
        // Toggle menu com INSERT
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            Animations::Menu::SetVisible(g_ShowMenu);
            
            // Configurar transparência da janela baseado na visibilidade do menu
            if (g_ShowMenu) {
                // Menu visível - permitir interação
                SetWindowLong(g_hWnd, GWL_EXSTYLE, GetWindowLong(g_hWnd, GWL_EXSTYLE) & ~WS_EX_TRANSPARENT);
            } else {
                // Menu oculto - mouse passa através
                SetWindowLong(g_hWnd, GWL_EXSTYLE, GetWindowLong(g_hWnd, GWL_EXSTYLE) | WS_EX_TRANSPARENT);
            }
        }
        
        // Fechar aplicação com ESC
        if (GetAsyncKeyState(VK_ESCAPE) & 1) {
            PostQuitMessage(0);
        }

        // Atualizar sistema de animações
        Animations::Update();
        Animations::Menu::Update();

        // Iniciar frame ImGui
        ImGui_ImplDX9_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Renderizar menu com animação
        if (Animations::Menu::ShouldRender()) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, Animations::Menu::GetAlpha());
            RenderMenu();
            ImGui::PopStyleVar();
        }

        ImGui::EndFrame();
        
        // Configurar estados de renderização para transparência
        g_pd3dDevice->SetRenderState(D3DRS_ZENABLE, FALSE);
        g_pd3dDevice->SetRenderState(D3DRS_ALPHABLENDENABLE, TRUE);
        g_pd3dDevice->SetRenderState(D3DRS_SRCBLEND, D3DBLEND_SRCALPHA);
        g_pd3dDevice->SetRenderState(D3DRS_DESTBLEND, D3DBLEND_INVSRCALPHA);
        g_pd3dDevice->SetRenderState(D3DRS_SCISSORTESTENABLE, FALSE);
        
        // Limpar com fundo completamente transparente
        D3DCOLOR clear_col_dx = D3DCOLOR_RGBA(0, 0, 0, 0);
        g_pd3dDevice->Clear(0, NULL, D3DCLEAR_TARGET | D3DCLEAR_ZBUFFER, clear_col_dx, 1.0f, 0);
        if (g_pd3dDevice->BeginScene() >= 0) {
            ImGui::Render();
            ImGui_ImplDX9_RenderDrawData(ImGui::GetDrawData());
            g_pd3dDevice->EndScene();
        }
        HRESULT result = g_pd3dDevice->Present(NULL, NULL, NULL, NULL);
        if (result == D3DERR_DEVICELOST && g_pd3dDevice->TestCooperativeLevel() == D3DERR_DEVICENOTRESET)
            ResetDevice();
    }

    // Limpeza
    ImGui_ImplDX9_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
    CleanupDeviceD3D();
    DestroyWindow(g_hWnd);
    UnregisterClass(wc.lpszClassName, wc.hInstance);
    return 0;
}

// Neverlose Style CS2 Cheat Menu - Professional Design
void RenderMenu() {
    if (!Animations::Menu::ShouldRender()) {
        return;
    }

    // Usar a nova UI Neverlose
    NeverloseUI::DrawNeverloseUI();
}

// WndProc para ImGui
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    // Comentado temporariamente para resolver erro de compilação
    // if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
    //     return true;
    switch (msg) {
    case WM_SIZE:
        if (g_pd3dDevice != NULL && wParam != SIZE_MINIMIZED) {
            g_d3dpp.BackBufferWidth = LOWORD(lParam);
            g_d3dpp.BackBufferHeight = HIWORD(lParam);
            ResetDevice();
        }
        return 0;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}