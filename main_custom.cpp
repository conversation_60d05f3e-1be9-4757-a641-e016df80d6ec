#include <iostream>
#include <windows.h>
#include "custom_gui.h"

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")

// Global variables
static HWND g_hWnd = NULL;
static bool g_ShowMenu = true;

// Prototypes
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Main code
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Create application window (overlay transparente)
    WNDCLASSEXA wc = {
        sizeof(WNDCLASSEXA),
        CS_CLASSDC | CS_HREDRAW | CS_VREDRAW,
        WndProc,
        0L, 0L,
        hInstance,
        NULL, NULL,
        NULL, // Sem fundo para transparência
        NULL,
        "CS2CheatMenuOverlay",
        NULL
    };
    RegisterClassExA(&wc);

    // Criar janela overlay (sem WS_EX_TRANSPARENT para permitir cliques)
    HWND hwnd = CreateWindowExA(
        WS_EX_LAYERED | WS_EX_TOPMOST, // Overlay sempre no topo, mas clicável
        wc.lpszClassName,
        "CS2 Cheat Menu - Professional Overlay",
        WS_POPUP, // Janela sem bordas
        100, 100, 800, 600, // Tamanho menor, não tela cheia
        NULL, NULL,
        hInstance, NULL
    );

    // Configurar transparência apenas para cor preta (fundo transparente)
    SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 0, LWA_COLORKEY); // Preto = transparente

    if (!hwnd) {
        std::cout << "Falha ao criar janela!" << std::endl;
        return 1;
    }

    // Show the window
    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);
    g_hWnd = hwnd;

    // Inicializar GUI customizada
    if (!CustomGUI::Initialize(hwnd)) {
        std::cout << "Falha ao inicializar GUI customizada!" << std::endl;
        return 1;
    }

    // Main loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Toggle menu com INSERT
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            InvalidateRect(hwnd, NULL, TRUE); // Forçar redesenho
            Sleep(150); // Evitar múltiplos toggles
        }

        // Fechar aplicação com ESC
        if (GetAsyncKeyState(VK_ESCAPE) & 1) {
            PostQuitMessage(0);
        }

        Sleep(16); // ~60 FPS
    }

    CustomGUI::Cleanup();
    DestroyWindow(hwnd);
    UnregisterClassA(wc.lpszClassName, hInstance);
    return 0;
}

// Win32 message handler
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);

            // Limpar fundo com preto (que ficará transparente)
            RECT rect;
            GetClientRect(hWnd, &rect);
            HBRUSH blackBrush = CreateSolidBrush(RGB(0, 0, 0));
            FillRect(hdc, &rect, blackBrush);
            DeleteObject(blackBrush);

            // Renderizar menu se visível
            if (g_ShowMenu) {
                CustomGUI::g_hdc = hdc;
                CustomGUI::g_hwnd = hWnd;
                CustomGUI::RenderMenu();
            }

            EndPaint(hWnd, &ps);
        }
        return 0;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}
