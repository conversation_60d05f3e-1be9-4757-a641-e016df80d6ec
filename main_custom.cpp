#include <iostream>
#include <windows.h>
#include "custom_gui.h"

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")

// Global variables
static HWND g_hWnd = NULL;
static bool g_ShowMenu = true;

// Prototypes
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Main code
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Create application window (overlay transparente)
    WNDCLASSEXA wc = {
        sizeof(WNDCLASSEXA),
        CS_CLASSDC | CS_HREDRAW | CS_VREDRAW,
        WndProc,
        0L, 0L,
        hInstance,
        NULL, NULL,
        NULL, // Sem fundo para transparência
        NULL,
        "CS2CheatMenuOverlay",
        NULL
    };
    RegisterClassExA(&wc);

    // Criar janela overlay SÓLIDA e clicável
    HWND hwnd = CreateWindowExA(
        WS_EX_TOPMOST, // Apenas sempre no topo, SEM transparência
        wc.lpszClassName,
        "CS2 Cheat Menu - Professional",
        WS_POPUP, // Janela sem bordas
        100, 100, 675, 500, // Tamanho exato do menu
        NULL, NULL,
        hInstance, NULL
    );

    // SEM SetLayeredWindowAttributes - janela totalmente sólida e clicável

    if (!hwnd) {
        std::cout << "Falha ao criar janela!" << std::endl;
        return 1;
    }

    // Show the window
    ShowWindow(hwnd, nCmdShow);
    UpdateWindow(hwnd);
    g_hWnd = hwnd;

    // Inicializar GUI customizada
    if (!CustomGUI::Initialize(hwnd)) {
        std::cout << "Falha ao inicializar GUI customizada!" << std::endl;
        return 1;
    }

    // Main loop
    MSG msg;
    ZeroMemory(&msg, sizeof(msg));
    while (msg.message != WM_QUIT) {
        if (PeekMessage(&msg, NULL, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            continue;
        }

        // Toggle menu com INSERT
        if (GetAsyncKeyState(VK_INSERT) & 1) {
            g_ShowMenu = !g_ShowMenu;
            InvalidateRect(hwnd, NULL, TRUE); // Forçar redesenho
            Sleep(150); // Evitar múltiplos toggles
        }

        // Fechar aplicação com ESC
        if (GetAsyncKeyState(VK_ESCAPE) & 1) {
            PostQuitMessage(0);
        }

        Sleep(16); // ~60 FPS
    }

    CustomGUI::Cleanup();
    DestroyWindow(hwnd);
    UnregisterClassA(wc.lpszClassName, hInstance);
    return 0;
}

// Win32 message handler
LRESULT WINAPI WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);

            // Limpar fundo com cor escura sólida
            RECT rect;
            GetClientRect(hWnd, &rect);
            HBRUSH bgBrush = CreateSolidBrush(RGB(15, 15, 20)); // Fundo escuro sólido
            FillRect(hdc, &rect, bgBrush);
            DeleteObject(bgBrush);

            // Renderizar menu se visível
            if (g_ShowMenu) {
                CustomGUI::g_hdc = hdc;
                CustomGUI::g_hwnd = hWnd;
                CustomGUI::RenderMenu();
            }

            EndPaint(hWnd, &ps);
        }
        return 0;
    case WM_LBUTTONDOWN:
        {
            // Permitir arrastar a janela clicando em qualquer lugar da barra superior
            POINT pt;
            GetCursorPos(&pt);
            ScreenToClient(hWnd, &pt);

            // Se clicar na área superior (barra de título), permitir drag
            if (pt.y <= 50) {
                // Simular clique na barra de título para arrastar
                ReleaseCapture();
                SendMessage(hWnd, WM_NCLBUTTONDOWN, HTCAPTION, 0);
            }
        }
        return 0;
    case WM_LBUTTONUP:
        // Liberar cliques do mouse
        return 0;
    case WM_MOUSEMOVE:
        // Capturar movimento do mouse
        return 0;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, msg, wParam, lParam);
}
