#pragma once
#include <algorithm>
#include <string>
#include <cmath>
#include "imgui.h"
#include "menu_style.h"
#include "animations.h"

namespace MenuComponents {
    
    // Cabeçalho estilizado com animação
    static void Header(const char* text) {
        // Texto centralizado com cor de destaque e animação
        ImVec2 windowSize = ImGui::GetWindowSize();
        ImVec2 textSize = ImGui::CalcTextSize(text);
        ImGui::SetCursorPosX((windowSize.x - textSize.x) * 0.5f);
        
        // Animação de entrada
        std::string key = std::string("header_") + text;
        Animations::SetTarget(key, 1.0f, 3.0f, 2); // EaseInOut
        float animValue = Animations::GetValue(key);
        
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(
            MenuStyle::Colors::Accent.x,
            MenuStyle::Colors::Accent.y,
            MenuStyle::Colors::Accent.z,
            MenuStyle::Colors::Accent.w * animValue
        ));
        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + (1.0f - animValue) * 10.0f); // Deslizar para baixo
        ImGui::Text("%s", text);
        ImGui::PopStyleColor();
        
        // Linha separadora animada
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 lineStart = ImGui::GetCursorScreenPos();
        lineStart.x -= textSize.x * 0.5f * animValue; // Expandir do centro
        
        ImVec2 lineEnd = lineStart;
        lineEnd.x += textSize.x * animValue;
        
        draw_list->AddLine(
            lineStart, 
            lineEnd, 
            ImGui::ColorConvertFloat4ToU32(MenuStyle::Colors::Accent), 
            2.0f
        );
        
        ImGui::Spacing();
    }
    
    // Seção com título animado
    static bool Section(const char* title, bool defaultOpen = true) {
        std::string key = std::string("section_") + title;
        Animations::SetTarget(key, 1.0f, 4.0f, 1); // EaseOut
        float animValue = Animations::GetValue(key);
        
        ImGui::PushStyleColor(ImGuiCol_Header, ImVec4(
            MenuStyle::Colors::Button.x,
            MenuStyle::Colors::Button.y,
            MenuStyle::Colors::Button.z,
            MenuStyle::Colors::Button.w * animValue
        ));
        ImGui::PushStyleColor(ImGuiCol_HeaderHovered, MenuStyle::Colors::ButtonHovered);
        ImGui::PushStyleColor(ImGuiCol_HeaderActive, MenuStyle::Colors::ButtonActive);
        
        bool result = ImGui::CollapsingHeader(title, defaultOpen ? ImGuiTreeNodeFlags_DefaultOpen : 0);
        
        ImGui::PopStyleColor(3);
        return result;
    }
    
    // Botão com ícone animado
    static bool IconButton(const char* icon, const char* label, const ImVec2& size = ImVec2(0, 0)) {
        std::string fullLabel = std::string(icon) + " " + label;
        return Animations::Elements::AnimatedButton(fullLabel.c_str(), size);
    }
    
    // Toggle switch moderno e animado
    static bool ToggleSwitch(const char* label, bool* v) {
        return Animations::Elements::AnimatedToggle(label, v);
    }
    
    // Slider colorido com animação
    static bool ColoredSlider(const char* label, float* v, float v_min, float v_max, const char* format = "%.1f") {
        // Determinar cor baseada no valor
        float normalized = (*v - v_min) / (v_max - v_min);
        ImVec4 sliderColor;
        
        if (normalized < 0.5f) {
            sliderColor = ImVec4(normalized * 2.0f, normalized * 2.0f, 1.0f - normalized * 2.0f, 1.0f);
        } else {
            float t = (normalized - 0.5f) * 2.0f;
            sliderColor = ImVec4(1.0f, 1.0f - t, 0.0f, 1.0f);
        }
        
        // Animação de hover
        std::string key = std::string("slider_") + label;
        bool hovered = ImGui::IsItemHovered();
        Animations::SetTarget(key, hovered ? 1.0f : 0.0f, 6.0f, 1); // EaseOut
        float animValue = Animations::GetValue(key);
        
        // Aumentar brilho quando hover
        float r = std::min(1.0f, sliderColor.x + 0.2f * animValue);
        float g = std::min(1.0f, sliderColor.y + 0.2f * animValue);
        float b = std::min(1.0f, sliderColor.z + 0.2f * animValue);
        sliderColor = ImVec4(r, g, b, sliderColor.w);
        
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, sliderColor);
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, sliderColor);
        
        bool result = ImGui::SliderFloat(label, v, v_min, v_max, format);
        
        ImGui::PopStyleColor(2);
        return result;
    }
    
    // Grupo de botões horizontais estilizados e animados
    static int ButtonGroup(const char* labels[], int count, int selected) {
        int result = selected;
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        
        // Calcular largura total
        float total_width = ImGui::GetContentRegionAvail().x;
        float button_width = (total_width - (count - 1) * 5) / count; // 5px de espaço entre botões
        
        for (int i = 0; i < count; i++) {
            if (i > 0) {
                ImGui::SameLine();
                ImGui::SetCursorPosX(ImGui::GetCursorPosX() + 5); // Espaçamento
            }
            
            bool isSelected = (i == selected);
            ImVec2 pos = ImGui::GetCursorScreenPos();
            
            // Animação de seleção
            std::string key = std::string("btngrp_") + labels[i];
            Animations::SetTarget(key, isSelected ? 1.0f : 0.0f, 6.0f, 2); // EaseInOut
            float animValue = Animations::GetValue(key);
            
            // Botão invisível para detecção de clique
            if (ImGui::InvisibleButton(labels[i], ImVec2(button_width, 35))) {
                result = i;
                // Animação de clique
                Animations::SetTarget(key + "_click", 1.0f, 12.0f, 3); // Bounce
            }
            
            // Animação de clique
            float clickAnim = Animations::GetValue(key + "_click");
            if (clickAnim > 0.01f) {
                Animations::SetTarget(key + "_click", 0.0f, 8.0f, 1); // EaseOut
            }
            
            bool hovered = ImGui::IsItemHovered();
            
            // Cores baseadas no estado com animação
            ImU32 bg_color = ImGui::ColorConvertFloat4ToU32(ImVec4(
                0.12f + 0.14f * animValue + (hovered ? 0.05f : 0.0f),
                0.12f + 0.14f * animValue + (hovered ? 0.05f : 0.0f),
                0.15f + 0.10f * animValue + (hovered ? 0.05f : 0.0f),
                0.9f + 0.1f * animValue
            ));
            
            ImU32 border_color = ImGui::ColorConvertFloat4ToU32(ImVec4(
                0.26f * animValue + 0.2f * (1.0f - animValue),
                0.59f * animValue + 0.2f * (1.0f - animValue),
                0.98f * animValue + 0.25f * (1.0f - animValue),
                0.7f + 0.3f * animValue
            ));
            
            ImU32 text_color = ImGui::ColorConvertFloat4ToU32(ImVec4(
                0.7f + 0.3f * animValue + (hovered ? 0.1f : 0.0f),
                0.7f + 0.3f * animValue + (hovered ? 0.1f : 0.0f),
                0.7f + 0.3f * animValue + (hovered ? 0.1f : 0.0f),
                1.0f
            ));
            
            // Efeito de escala no clique
            float scale = 1.0f - clickAnim * 0.05f;
            float scaledWidth = button_width * scale;
            float offsetX = (button_width - scaledWidth) * 0.5f;
            
            // Desenhar fundo do botão
            draw_list->AddRectFilled(
                ImVec2(pos.x + offsetX, pos.y), 
                ImVec2(pos.x + offsetX + scaledWidth, pos.y + 35 * scale), 
                bg_color, 8.0f
            );
            
            // Borda com glow para selecionado
            if (animValue > 0.01f) {
                draw_list->AddRect(
                    ImVec2(pos.x + offsetX, pos.y), 
                    ImVec2(pos.x + offsetX + scaledWidth, pos.y + 35 * scale), 
                    border_color, 8.0f, 0, 1.5f
                );
            }
            
            // Desenhar texto centralizado
            ImVec2 text_size = ImGui::CalcTextSize(labels[i]);
            ImVec2 text_pos = ImVec2(
                pos.x + offsetX + (scaledWidth - text_size.x) * 0.5f,
                pos.y + (35 * scale - text_size.y) * 0.5f
            );
            draw_list->AddText(text_pos, text_color, labels[i]);
            
            // Indicador de seleção animado na parte inferior
            if (animValue > 0.01f) {
                float indicatorHeight = 3.0f * animValue;
                draw_list->AddRectFilled(
                    ImVec2(pos.x + offsetX, pos.y + 35 * scale - indicatorHeight),
                    ImVec2(pos.x + offsetX + scaledWidth, pos.y + 35 * scale),
                    ImGui::ColorConvertFloat4ToU32(ImVec4(0.26f, 0.59f, 0.98f, animValue)),
                    0.0f
                );
            }
        }
        
        return result;
    }
    
    // Status indicator animado
    static void StatusIndicator(const char* label, bool active) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 pos = ImGui::GetCursorScreenPos();
        
        // Animação de estado
        std::string key = std::string("status_") + label;
        Animations::SetTarget(key, active ? 1.0f : 0.0f, 4.0f, 2); // EaseInOut
        float animValue = Animations::GetValue(key);
        
        // Interpolar cores
        ImVec4 inactiveColor = ImVec4(0.7f, 0.2f, 0.2f, 1.0f); // Vermelho
        ImVec4 activeColor = ImVec4(0.2f, 0.8f, 0.2f, 1.0f);   // Verde
        
        ImVec4 currentColor = ImVec4(
            inactiveColor.x + (activeColor.x - inactiveColor.x) * animValue,
            inactiveColor.y + (activeColor.y - inactiveColor.y) * animValue,
            inactiveColor.z + (activeColor.z - inactiveColor.z) * animValue,
            inactiveColor.w
        );
        
        // Círculo pulsante
        float baseRadius = 4.0f;
        float pulseAnim = (std::sin(ImGui::GetTime() * 6.0f) * 0.5f + 0.5f) * animValue;
        float radius = baseRadius * (1.0f + 0.3f * pulseAnim);
        
        // Glow ao redor do círculo
        if (active) {
            draw_list->AddCircleFilled(
                ImVec2(pos.x + baseRadius, pos.y + ImGui::GetTextLineHeight() * 0.5f),
                radius + 2.0f,
                ImGui::ColorConvertFloat4ToU32(ImVec4(currentColor.x, currentColor.y, currentColor.z, 0.3f * pulseAnim))
            );
        }
        
        // Círculo principal
        draw_list->AddCircleFilled(
            ImVec2(pos.x + baseRadius, pos.y + ImGui::GetTextLineHeight() * 0.5f),
            radius,
            ImGui::ColorConvertFloat4ToU32(currentColor)
        );
        
        // Texto
        ImGui::SetCursorPosX(ImGui::GetCursorPosX() + baseRadius * 2 + 8);
        ImGui::PushStyleColor(ImGuiCol_Text, currentColor);
        ImGui::Text("%s", label);
        ImGui::PopStyleColor();
    }
    
    // Progress bar estilizada com animação
    static void StyledProgressBar(float fraction, const ImVec2& size_arg = ImVec2(-1, 0), const char* overlay = NULL) {
        // Animação de preenchimento
        static std::unordered_map<const char*, float> progressAnims;
        std::string key = overlay ? std::string("progress_") + overlay : "progress_default";
        
        if (progressAnims.find(key.c_str()) == progressAnims.end()) {
            progressAnims[key.c_str()] = 0.0f;
        }
        
        Animations::SetTarget(key, fraction, 3.0f, 2); // EaseInOut
        float animValue = Animations::GetValue(key);
        progressAnims[key.c_str()] = animValue;
        
        // Gradiente de cores baseado no valor
        ImVec4 progressColor;
        if (animValue < 0.5f) {
            progressColor = ImVec4(1.0f - animValue * 2.0f, animValue * 2.0f, 0.0f, 1.0f); // Vermelho -> Amarelo
        } else {
            progressColor = ImVec4(0.0f, 1.0f, (animValue - 0.5f) * 2.0f, 1.0f); // Amarelo -> Verde
        }
        
        ImGui::PushStyleColor(ImGuiCol_PlotHistogram, progressColor);
        ImGui::ProgressBar(animValue, size_arg, overlay);
        ImGui::PopStyleColor();
    }
}
