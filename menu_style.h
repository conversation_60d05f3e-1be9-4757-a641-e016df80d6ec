#pragma once
#include "imgui.h"

namespace MenuStyle {
    // Cores do tema moderno
    struct Colors {
        static constexpr ImVec4 Background      = ImVec4(0.06f, 0.06f, 0.08f, 0.95f);
        static constexpr ImVec4 ChildBg         = ImVec4(0.08f, 0.08f, 0.10f, 0.95f);
        static constexpr ImVec4 Border          = ImVec4(0.20f, 0.20f, 0.25f, 0.60f);
        static constexpr ImVec4 Text            = ImVec4(0.95f, 0.95f, 0.95f, 1.00f);
        static constexpr ImVec4 TextDisabled    = ImVec4(0.50f, 0.50f, 0.50f, 1.00f);
        
        // Elementos interativos
        static constexpr ImVec4 Button          = ImVec4(0.12f, 0.12f, 0.15f, 1.00f);
        static constexpr ImVec4 ButtonHovered   = ImVec4(0.20f, 0.20f, 0.25f, 1.00f);
        static constexpr ImVec4 ButtonActive    = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
        
        // Acentos coloridos
        static constexpr ImVec4 Accent          = ImVec4(0.26f, 0.59f, 0.98f, 1.00f); // Azul
        static constexpr ImVec4 AccentHovered   = ImVec4(0.36f, 0.69f, 1.00f, 1.00f);
        static constexpr ImVec4 Success         = ImVec4(0.20f, 0.80f, 0.20f, 1.00f); // Verde
        static constexpr ImVec4 Warning         = ImVec4(1.00f, 0.70f, 0.00f, 1.00f); // Laranja
        static constexpr ImVec4 Danger          = ImVec4(0.90f, 0.20f, 0.20f, 1.00f); // Vermelho
    };

    // Configurar estilo moderno e futurista
    static void SetupStyle() {
        ImGuiStyle& style = ImGui::GetStyle();
        
        // Bordas e arredondamento - mais moderno
        style.WindowRounding     = 15.0f;
        style.ChildRounding      = 10.0f;
        style.FrameRounding      = 8.0f;
        style.PopupRounding      = 8.0f;
        style.ScrollbarRounding  = 12.0f;
        style.GrabRounding       = 8.0f;
        style.TabRounding        = 8.0f;
        
        // Espessuras
        style.WindowBorderSize   = 1.0f;
        style.ChildBorderSize    = 1.0f;
        style.PopupBorderSize    = 1.0f;
        style.FrameBorderSize    = 0.0f;
        style.TabBorderSize      = 0.0f;
        
        // Espaçamentos - mais generosos
        style.WindowPadding      = ImVec2(20, 20);
        style.FramePadding       = ImVec2(12, 8);
        style.CellPadding        = ImVec2(8, 8);
        style.ItemSpacing        = ImVec2(12, 10);
        style.ItemInnerSpacing   = ImVec2(8, 8);
        style.TouchExtraPadding  = ImVec2(0, 0);
        style.IndentSpacing      = 30;
        style.ScrollbarSize      = 18;
        style.GrabMinSize        = 12;
        
        // Alinhamentos
        style.WindowTitleAlign   = ImVec2(0.50f, 0.50f);
        style.WindowMenuButtonPosition = ImGuiDir_Left;
        style.ButtonTextAlign    = ImVec2(0.50f, 0.50f);
        style.SelectableTextAlign = ImVec2(0.00f, 0.00f);
        
        // Aplicar cores
        ImVec4* colors = style.Colors;
        colors[ImGuiCol_Text]                   = Colors::Text;
        colors[ImGuiCol_TextDisabled]           = Colors::TextDisabled;
        colors[ImGuiCol_WindowBg]               = Colors::Background;
        colors[ImGuiCol_ChildBg]                = Colors::ChildBg;
        colors[ImGuiCol_PopupBg]                = Colors::Background;
        colors[ImGuiCol_Border]                 = Colors::Border;
        colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_FrameBg]                = ImVec4(0.12f, 0.12f, 0.15f, 1.00f);
        colors[ImGuiCol_FrameBgHovered]         = ImVec4(0.20f, 0.20f, 0.25f, 1.00f);
        colors[ImGuiCol_FrameBgActive]          = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
        colors[ImGuiCol_TitleBg]                = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
        colors[ImGuiCol_TitleBgActive]          = ImVec4(0.12f, 0.12f, 0.15f, 1.00f);
        colors[ImGuiCol_TitleBgCollapsed]       = ImVec4(0.08f, 0.08f, 0.10f, 0.75f);
        colors[ImGuiCol_MenuBarBg]              = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
        colors[ImGuiCol_ScrollbarBg]            = ImVec4(0.02f, 0.02f, 0.02f, 0.53f);
        colors[ImGuiCol_ScrollbarGrab]          = ImVec4(0.31f, 0.31f, 0.31f, 1.00f);
        colors[ImGuiCol_ScrollbarGrabHovered]   = ImVec4(0.41f, 0.41f, 0.41f, 1.00f);
        colors[ImGuiCol_ScrollbarGrabActive]    = ImVec4(0.51f, 0.51f, 0.51f, 1.00f);
        colors[ImGuiCol_CheckMark]              = Colors::Accent;
        colors[ImGuiCol_SliderGrab]             = Colors::Accent;
        colors[ImGuiCol_SliderGrabActive]       = Colors::AccentHovered;
        colors[ImGuiCol_Button]                 = Colors::Button;
        colors[ImGuiCol_ButtonHovered]          = Colors::ButtonHovered;
        colors[ImGuiCol_ButtonActive]           = Colors::ButtonActive;
        colors[ImGuiCol_Header]                 = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
        colors[ImGuiCol_HeaderHovered]          = ImVec4(0.15f, 0.15f, 0.18f, 1.00f);
        colors[ImGuiCol_HeaderActive]           = ImVec4(0.12f, 0.12f, 0.15f, 1.00f);
        colors[ImGuiCol_Separator]              = Colors::Border;
        colors[ImGuiCol_SeparatorHovered]       = Colors::Accent;
        colors[ImGuiCol_SeparatorActive]        = Colors::AccentHovered;
        colors[ImGuiCol_ResizeGrip]             = ImVec4(0.26f, 0.59f, 0.98f, 0.20f);
        colors[ImGuiCol_ResizeGripHovered]      = ImVec4(0.26f, 0.59f, 0.98f, 0.67f);
        colors[ImGuiCol_ResizeGripActive]       = ImVec4(0.26f, 0.59f, 0.98f, 0.95f);
        colors[ImGuiCol_Tab]                    = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
        colors[ImGuiCol_TabHovered]             = Colors::ButtonHovered;
        colors[ImGuiCol_TabActive]              = ImVec4(0.15f, 0.15f, 0.18f, 1.00f);
        colors[ImGuiCol_TabUnfocused]           = ImVec4(0.08f, 0.08f, 0.10f, 1.00f);
        colors[ImGuiCol_TabUnfocusedActive]     = ImVec4(0.12f, 0.12f, 0.15f, 1.00f);
        colors[ImGuiCol_PlotLines]              = ImVec4(0.61f, 0.61f, 0.61f, 1.00f);
        colors[ImGuiCol_PlotLinesHovered]       = ImVec4(1.00f, 0.43f, 0.35f, 1.00f);
        colors[ImGuiCol_PlotHistogram]          = ImVec4(0.90f, 0.70f, 0.00f, 1.00f);
        colors[ImGuiCol_PlotHistogramHovered]   = ImVec4(1.00f, 0.60f, 0.00f, 1.00f);
        colors[ImGuiCol_TableHeaderBg]          = ImVec4(0.19f, 0.19f, 0.20f, 1.00f);
        colors[ImGuiCol_TableBorderStrong]      = ImVec4(0.31f, 0.31f, 0.35f, 1.00f);
        colors[ImGuiCol_TableBorderLight]       = ImVec4(0.23f, 0.23f, 0.25f, 1.00f);
        colors[ImGuiCol_TableRowBg]             = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_TableRowBgAlt]          = ImVec4(1.00f, 1.00f, 1.00f, 0.06f);
        colors[ImGuiCol_TextSelectedBg]         = ImVec4(0.26f, 0.59f, 0.98f, 0.35f);
        colors[ImGuiCol_DragDropTarget]         = ImVec4(1.00f, 1.00f, 0.00f, 0.90f);
        colors[ImGuiCol_NavHighlight]           = ImVec4(0.26f, 0.59f, 0.98f, 1.00f);
        colors[ImGuiCol_NavWindowingHighlight]  = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
        colors[ImGuiCol_NavWindowingDimBg]      = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);
        colors[ImGuiCol_ModalWindowDimBg]       = ImVec4(0.80f, 0.80f, 0.80f, 0.35f);
    }
}
