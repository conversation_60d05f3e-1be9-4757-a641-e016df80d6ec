#pragma once
#include "imgui.h"

namespace MenuStyle {
    // Cores do tema Neverlose - design profissional
    struct Colors {
        // Paleta principal Neverlose
        static constexpr ImVec4 Background      = ImVec4(0.00f, 0.00f, 0.00f, 0.75f); // Fundo principal preto 75%
        static constexpr ImVec4 ChildBg         = ImVec4(0.04f, 0.04f, 0.07f, 0.50f); // Fundo das seções
        static constexpr ImVec4 Surface         = ImVec4(0.06f, 0.05f, 0.08f, 0.75f); // Superfície elevada
        static constexpr ImVec4 Border          = ImVec4(0.00f, 0.00f, 0.00f, 0.00f); // Sem bordas

        // Tipografia Neverlose
        static constexpr ImVec4 Text            = ImVec4(1.00f, 1.00f, 1.00f, 1.00f); // Branco puro
        static constexpr ImVec4 TextSecondary   = ImVec4(1.00f, 1.00f, 1.00f, 0.75f); // Branco 75%
        static constexpr ImVec4 TextDisabled    = ImVec4(1.00f, 1.00f, 1.00f, 0.25f); // Branco 25%

        // Elementos interativos Neverlose
        static constexpr ImVec4 Button          = ImVec4(0.15f, 0.15f, 0.22f, 0.50f);
        static constexpr ImVec4 ButtonHovered   = ImVec4(0.20f, 0.20f, 0.30f, 0.60f);
        static constexpr ImVec4 ButtonActive    = ImVec4(0.32f, 0.48f, 0.98f, 1.00f); // Azul Neverlose

        // Accent color Neverlose (azul característico)
        static constexpr ImVec4 Accent          = ImVec4(0.32f, 0.48f, 0.98f, 1.00f); // #527BFB
        static constexpr ImVec4 AccentHovered   = ImVec4(0.42f, 0.58f, 1.00f, 1.00f);
        static constexpr ImVec4 AccentMuted     = ImVec4(0.32f, 0.48f, 0.98f, 0.20f);

        // Estados de sistema
        static constexpr ImVec4 Success         = ImVec4(0.30f, 0.85f, 0.40f, 1.00f);
        static constexpr ImVec4 Warning         = ImVec4(1.00f, 0.72f, 0.00f, 1.00f); // #FFB800
        static constexpr ImVec4 Danger          = ImVec4(1.00f, 0.40f, 0.40f, 1.00f);
    };

    // Configurar estilo baseado no design Neverlose
    static void SetupStyle() {
        ImGuiStyle& style = ImGui::GetStyle();

        // Bordas e arredondamento Neverlose
        style.WindowRounding     = 5.0f;   // Cantos sutis como no design
        style.ChildRounding      = 5.0f;   // Elementos filhos consistentes
        style.FrameRounding      = 5.0f;   // Campos de entrada limpos
        style.PopupRounding      = 5.0f;   // Popups consistentes
        style.ScrollbarRounding  = 6.0f;   // Scrollbars suaves
        style.GrabRounding       = 3.0f;   // Sliders limpos
        style.TabRounding        = 5.0f;   // Tabs consistentes

        // Espessuras Neverlose (sem bordas)
        style.WindowBorderSize   = 0.0f;   // Sem bordas nas janelas
        style.ChildBorderSize    = 0.0f;   // Sem bordas nos elementos filhos
        style.PopupBorderSize    = 0.0f;   // Sem bordas nos popups
        style.FrameBorderSize    = 0.0f;   // Campos sem bordas
        style.TabBorderSize      = 0.0f;   // Tabs sem bordas

        // Espaçamentos Neverlose - compactos e profissionais
        style.WindowPadding      = ImVec2(0, 0);    // Sem padding na janela principal
        style.FramePadding       = ImVec2(10, 6);   // Campos confortáveis
        style.CellPadding        = ImVec2(8, 4);    // Células bem espaçadas
        style.ItemSpacing        = ImVec2(10, 8);   // Espaçamento entre itens
        style.ItemInnerSpacing   = ImVec2(6, 4);    // Espaçamento interno
        style.TouchExtraPadding  = ImVec2(0, 0);    // Sem padding extra
        style.IndentSpacing      = 20;              // Indentação consistente
        style.ScrollbarSize      = 12;              // Scrollbar discreta
        style.GrabMinSize        = 12;              // Sliders fáceis de usar

        // Alinhamentos Neverlose
        style.WindowTitleAlign   = ImVec2(0.5f, 0.5f);    // Título centralizado
        style.WindowMenuButtonPosition = ImGuiDir_None;    // Sem botão de menu
        style.ButtonTextAlign    = ImVec2(0.5f, 0.5f);    // Texto centralizado nos botões
        style.SelectableTextAlign = ImVec2(0.0f, 0.5f);   // Texto alinhado à esquerda
        style.ColorButtonPosition = ImGuiDir_Right;        // Botão de cor à direita

        // Aplicar paleta de cores minimalista
        ImVec4* colors = style.Colors;
        colors[ImGuiCol_Text]                   = Colors::Text;
        colors[ImGuiCol_TextDisabled]           = Colors::TextDisabled;
        colors[ImGuiCol_WindowBg]               = Colors::Background;
        colors[ImGuiCol_ChildBg]                = Colors::ChildBg;
        colors[ImGuiCol_PopupBg]                = Colors::Surface;
        colors[ImGuiCol_Border]                 = Colors::Border;
        colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_FrameBg]                = Colors::Surface;
        colors[ImGuiCol_FrameBgHovered]         = Colors::ButtonHovered;
        colors[ImGuiCol_FrameBgActive]          = Colors::ButtonActive;
        colors[ImGuiCol_TitleBg]                = Colors::Background;
        colors[ImGuiCol_TitleBgActive]          = Colors::ChildBg;
        colors[ImGuiCol_TitleBgCollapsed]       = Colors::Background;
        colors[ImGuiCol_MenuBarBg]              = Colors::ChildBg;
        colors[ImGuiCol_ScrollbarBg]            = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_ScrollbarGrab]          = Colors::TextDisabled;
        colors[ImGuiCol_ScrollbarGrabHovered]   = Colors::TextSecondary;
        colors[ImGuiCol_ScrollbarGrabActive]    = Colors::Text;
        colors[ImGuiCol_CheckMark]              = Colors::Accent;
        colors[ImGuiCol_SliderGrab]             = Colors::Accent;
        colors[ImGuiCol_SliderGrabActive]       = Colors::AccentHovered;
        colors[ImGuiCol_Button]                 = Colors::Button;
        colors[ImGuiCol_ButtonHovered]          = Colors::ButtonHovered;
        colors[ImGuiCol_ButtonActive]           = Colors::ButtonActive;
        colors[ImGuiCol_Header]                 = Colors::Surface;
        colors[ImGuiCol_HeaderHovered]          = Colors::ButtonHovered;
        colors[ImGuiCol_HeaderActive]           = Colors::ButtonActive;
        colors[ImGuiCol_Separator]              = Colors::Border;
        colors[ImGuiCol_SeparatorHovered]       = Colors::Accent;
        colors[ImGuiCol_SeparatorActive]        = Colors::AccentHovered;
        colors[ImGuiCol_ResizeGrip]             = Colors::AccentMuted;
        colors[ImGuiCol_ResizeGripHovered]      = Colors::Accent;
        colors[ImGuiCol_ResizeGripActive]       = Colors::AccentHovered;
        colors[ImGuiCol_Tab]                    = Colors::Background;
        colors[ImGuiCol_TabHovered]             = Colors::ButtonHovered;
        colors[ImGuiCol_TabActive]              = Colors::Surface;
        colors[ImGuiCol_TabUnfocused]           = Colors::Background;
        colors[ImGuiCol_TabUnfocusedActive]     = Colors::ChildBg;
        colors[ImGuiCol_PlotLines]              = Colors::TextSecondary;
        colors[ImGuiCol_PlotLinesHovered]       = Colors::Accent;
        colors[ImGuiCol_PlotHistogram]          = Colors::Accent;
        colors[ImGuiCol_PlotHistogramHovered]   = Colors::AccentHovered;
        colors[ImGuiCol_TableHeaderBg]          = Colors::Surface;
        colors[ImGuiCol_TableBorderStrong]      = Colors::Border;
        colors[ImGuiCol_TableBorderLight]       = Colors::Border;
        colors[ImGuiCol_TableRowBg]             = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_TableRowBgAlt]          = ImVec4(1.00f, 1.00f, 1.00f, 0.02f);
        colors[ImGuiCol_TextSelectedBg]         = Colors::AccentMuted;
        colors[ImGuiCol_DragDropTarget]         = Colors::Accent;
        colors[ImGuiCol_NavHighlight]           = Colors::Accent;
        colors[ImGuiCol_NavWindowingHighlight]  = Colors::Text;
        colors[ImGuiCol_NavWindowingDimBg]      = ImVec4(0.00f, 0.00f, 0.00f, 0.60f);
        colors[ImGuiCol_ModalWindowDimBg]       = ImVec4(0.00f, 0.00f, 0.00f, 0.70f);
    }
}
