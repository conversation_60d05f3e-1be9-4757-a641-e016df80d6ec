#pragma once
#include "imgui.h"

namespace MenuStyle {
    // Cores do tema minimalista e profissional
    struct Colors {
        // Paleta de cinzas minimalista
        static constexpr ImVec4 Background      = ImVec4(0.04f, 0.04f, 0.05f, 0.98f); // Quase preto
        static constexpr ImVec4 ChildBg         = ImVec4(0.06f, 0.06f, 0.07f, 0.98f); // Cinza muito escuro
        static constexpr ImVec4 Surface         = ImVec4(0.08f, 0.08f, 0.09f, 1.00f); // Superfície elevada
        static constexpr ImVec4 Border          = ImVec4(0.15f, 0.15f, 0.16f, 0.80f); // Borda sutil

        // Tipografia minimalista
        static constexpr ImVec4 Text            = ImVec4(0.98f, 0.98f, 0.98f, 1.00f); // Branco quase puro
        static constexpr ImVec4 TextSecondary   = ImVec4(0.70f, 0.70f, 0.72f, 1.00f); // Cinza médio
        static constexpr ImVec4 TextDisabled    = ImVec4(0.45f, 0.45f, 0.47f, 1.00f); // Cinza claro

        // Elementos interativos minimalistas
        static constexpr ImVec4 Button          = ImVec4(0.10f, 0.10f, 0.11f, 1.00f);
        static constexpr ImVec4 ButtonHovered   = ImVec4(0.14f, 0.14f, 0.15f, 1.00f);
        static constexpr ImVec4 ButtonActive    = ImVec4(0.12f, 0.12f, 0.13f, 1.00f);

        // Accent color profissional (azul sutil)
        static constexpr ImVec4 Accent          = ImVec4(0.40f, 0.60f, 1.00f, 1.00f); // Azul profissional
        static constexpr ImVec4 AccentHovered   = ImVec4(0.50f, 0.70f, 1.00f, 1.00f);
        static constexpr ImVec4 AccentMuted     = ImVec4(0.40f, 0.60f, 1.00f, 0.20f);

        // Estados de sistema
        static constexpr ImVec4 Success         = ImVec4(0.30f, 0.85f, 0.40f, 1.00f); // Verde sutil
        static constexpr ImVec4 Warning         = ImVec4(1.00f, 0.75f, 0.20f, 1.00f); // Amarelo profissional
        static constexpr ImVec4 Danger          = ImVec4(1.00f, 0.40f, 0.40f, 1.00f); // Vermelho sutil
    };

    // Configurar estilo minimalista e profissional
    static void SetupStyle() {
        ImGuiStyle& style = ImGui::GetStyle();

        // Bordas e arredondamento minimalistas
        style.WindowRounding     = 12.0f;  // Cantos suavemente arredondados
        style.ChildRounding      = 8.0f;   // Elementos filhos mais sutis
        style.FrameRounding      = 6.0f;   // Campos de entrada limpos
        style.PopupRounding      = 8.0f;   // Popups consistentes
        style.ScrollbarRounding  = 10.0f;  // Scrollbars suaves
        style.GrabRounding       = 6.0f;   // Sliders limpos
        style.TabRounding        = 6.0f;   // Tabs minimalistas

        // Espessuras minimalistas
        style.WindowBorderSize   = 0.0f;   // Sem bordas nas janelas
        style.ChildBorderSize    = 0.0f;   // Sem bordas nos elementos filhos
        style.PopupBorderSize    = 1.0f;   // Borda sutil nos popups
        style.FrameBorderSize    = 0.0f;   // Campos sem bordas
        style.TabBorderSize      = 0.0f;   // Tabs sem bordas

        // Espaçamentos profissionais e limpos
        style.WindowPadding      = ImVec2(24, 24);  // Padding generoso
        style.FramePadding       = ImVec2(16, 10);  // Campos confortáveis
        style.CellPadding        = ImVec2(12, 8);   // Células bem espaçadas
        style.ItemSpacing        = ImVec2(16, 12);  // Espaçamento entre itens
        style.ItemInnerSpacing   = ImVec2(8, 6);    // Espaçamento interno
        style.TouchExtraPadding  = ImVec2(0, 0);    // Sem padding extra
        style.IndentSpacing      = 24;              // Indentação consistente
        style.ScrollbarSize      = 14;              // Scrollbar discreta
        style.GrabMinSize        = 14;              // Sliders fáceis de usar
        
        // Alinhamentos minimalistas
        style.WindowTitleAlign   = ImVec2(0.02f, 0.50f);  // Título alinhado à esquerda
        style.WindowMenuButtonPosition = ImGuiDir_Right;   // Botão do menu à direita
        style.ButtonTextAlign    = ImVec2(0.50f, 0.50f);  // Texto centralizado nos botões
        style.SelectableTextAlign = ImVec2(0.00f, 0.50f); // Texto alinhado à esquerda

        // Aplicar paleta de cores minimalista
        ImVec4* colors = style.Colors;
        colors[ImGuiCol_Text]                   = Colors::Text;
        colors[ImGuiCol_TextDisabled]           = Colors::TextDisabled;
        colors[ImGuiCol_WindowBg]               = Colors::Background;
        colors[ImGuiCol_ChildBg]                = Colors::ChildBg;
        colors[ImGuiCol_PopupBg]                = Colors::Surface;
        colors[ImGuiCol_Border]                 = Colors::Border;
        colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_FrameBg]                = Colors::Surface;
        colors[ImGuiCol_FrameBgHovered]         = Colors::ButtonHovered;
        colors[ImGuiCol_FrameBgActive]          = Colors::ButtonActive;
        colors[ImGuiCol_TitleBg]                = Colors::Background;
        colors[ImGuiCol_TitleBgActive]          = Colors::ChildBg;
        colors[ImGuiCol_TitleBgCollapsed]       = Colors::Background;
        colors[ImGuiCol_MenuBarBg]              = Colors::ChildBg;
        colors[ImGuiCol_ScrollbarBg]            = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_ScrollbarGrab]          = Colors::TextDisabled;
        colors[ImGuiCol_ScrollbarGrabHovered]   = Colors::TextSecondary;
        colors[ImGuiCol_ScrollbarGrabActive]    = Colors::Text;
        colors[ImGuiCol_CheckMark]              = Colors::Accent;
        colors[ImGuiCol_SliderGrab]             = Colors::Accent;
        colors[ImGuiCol_SliderGrabActive]       = Colors::AccentHovered;
        colors[ImGuiCol_Button]                 = Colors::Button;
        colors[ImGuiCol_ButtonHovered]          = Colors::ButtonHovered;
        colors[ImGuiCol_ButtonActive]           = Colors::ButtonActive;
        colors[ImGuiCol_Header]                 = Colors::Surface;
        colors[ImGuiCol_HeaderHovered]          = Colors::ButtonHovered;
        colors[ImGuiCol_HeaderActive]           = Colors::ButtonActive;
        colors[ImGuiCol_Separator]              = Colors::Border;
        colors[ImGuiCol_SeparatorHovered]       = Colors::Accent;
        colors[ImGuiCol_SeparatorActive]        = Colors::AccentHovered;
        colors[ImGuiCol_ResizeGrip]             = Colors::AccentMuted;
        colors[ImGuiCol_ResizeGripHovered]      = Colors::Accent;
        colors[ImGuiCol_ResizeGripActive]       = Colors::AccentHovered;
        colors[ImGuiCol_Tab]                    = Colors::Background;
        colors[ImGuiCol_TabHovered]             = Colors::ButtonHovered;
        colors[ImGuiCol_TabActive]              = Colors::Surface;
        colors[ImGuiCol_TabUnfocused]           = Colors::Background;
        colors[ImGuiCol_TabUnfocusedActive]     = Colors::ChildBg;
        colors[ImGuiCol_PlotLines]              = Colors::TextSecondary;
        colors[ImGuiCol_PlotLinesHovered]       = Colors::Accent;
        colors[ImGuiCol_PlotHistogram]          = Colors::Accent;
        colors[ImGuiCol_PlotHistogramHovered]   = Colors::AccentHovered;
        colors[ImGuiCol_TableHeaderBg]          = Colors::Surface;
        colors[ImGuiCol_TableBorderStrong]      = Colors::Border;
        colors[ImGuiCol_TableBorderLight]       = Colors::Border;
        colors[ImGuiCol_TableRowBg]             = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
        colors[ImGuiCol_TableRowBgAlt]          = ImVec4(1.00f, 1.00f, 1.00f, 0.02f);
        colors[ImGuiCol_TextSelectedBg]         = Colors::AccentMuted;
        colors[ImGuiCol_DragDropTarget]         = Colors::Accent;
        colors[ImGuiCol_NavHighlight]           = Colors::Accent;
        colors[ImGuiCol_NavWindowingHighlight]  = Colors::Text;
        colors[ImGuiCol_NavWindowingDimBg]      = ImVec4(0.00f, 0.00f, 0.00f, 0.60f);
        colors[ImGuiCol_ModalWindowDimBg]       = ImVec4(0.00f, 0.00f, 0.00f, 0.70f);
    }
}
