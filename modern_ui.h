#pragma once
#include "imgui.h"
#include <string>

namespace ModernUI {
    // Ícones usando caracteres Unicode simples
    namespace Icons {
        static const char* Target = "🎯";      // Crosshairs
        static const char* Eye = "👁";         // Eye
        static const char* Cog = "⚙";         // Settings
        static const char* Folder = "📁";      // Folder
        static const char* Bolt = "⚡";        // Lightning
        static const char* Shield = "🛡";      // Shield
        static const char* Radar = "📡";       // Radar
        static const char* Save = "💾";        // Save
        static const char* Load = "📂";        // Folder Open
        static const char* Check = "✓";        // Check
        static const char* Times = "✗";        // Times
        static const char* Circle = "●";       // Circle
        static const char* Home = "🏠";        // Home
        static const char* Speed = "🚀";       // Speed
        static const char* Misc = "🔧";        // Tools
    }
    
    // Cores do tema moderno
    struct Colors {
        static constexpr ImU32 Primary = IM_COL32(99, 102, 241, 255);      // Indigo
        static constexpr ImU32 PrimaryHover = IM_COL32(129, 140, 248, 255); // Indigo Light
        static constexpr ImU32 Secondary = IM_COL32(107, 114, 128, 255);    // Gray
        static constexpr ImU32 Background = IM_COL32(17, 24, 39, 250);      // Dark Blue
        static constexpr ImU32 Surface = IM_COL32(31, 41, 55, 255);        // Dark Surface
        static constexpr ImU32 SurfaceHover = IM_COL32(55, 65, 81, 255);   // Surface Hover
        static constexpr ImU32 Border = IM_COL32(75, 85, 99, 255);         // Border
        static constexpr ImU32 Text = IM_COL32(243, 244, 246, 255);        // Light Text
        static constexpr ImU32 TextMuted = IM_COL32(156, 163, 175, 255);   // Muted Text
        static constexpr ImU32 Success = IM_COL32(34, 197, 94, 255);       // Green
        static constexpr ImU32 Warning = IM_COL32(251, 191, 36, 255);      // Yellow
        static constexpr ImU32 Error = IM_COL32(239, 68, 68, 255);         // Red
        static constexpr ImU32 Transparent = IM_COL32(0, 0, 0, 0);         // Transparent
    };
    
    // Configurações de layout
    struct Layout {
        static constexpr float SidebarWidth = 200.0f;
        static constexpr float HeaderHeight = 60.0f;
        static constexpr float ItemHeight = 40.0f;
        static constexpr float Padding = 16.0f;
        static constexpr float Rounding = 8.0f;
        static constexpr float BorderWidth = 1.0f;
    };
    
    // Item da sidebar
    struct SidebarItem {
        const char* icon;
        const char* label;
        int id;
        bool active;
    };
    
    // Renderizar sidebar moderna com detecção de clique correta
    static int RenderSidebar(SidebarItem items[], int itemCount, int selectedItem) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 window_pos = ImGui::GetWindowPos();
        ImVec2 window_size = ImGui::GetWindowSize();
        
        // Fundo da sidebar com gradiente
        ImVec2 sidebar_min = window_pos;
        ImVec2 sidebar_max = ImVec2(window_pos.x + Layout::SidebarWidth, window_pos.y + window_size.y);
        
        // Gradiente vertical na sidebar
        draw_list->AddRectFilledMultiColor(
            sidebar_min, sidebar_max,
            Colors::Surface,     // Top left
            Colors::Surface,     // Top right  
            Colors::Background,  // Bottom right
            Colors::Background   // Bottom left
        );
        
        // Linha separadora com glow
        ImVec2 separator_start = ImVec2(window_pos.x + Layout::SidebarWidth - 1, window_pos.y);
        ImVec2 separator_end = ImVec2(window_pos.x + Layout::SidebarWidth - 1, window_pos.y + window_size.y);
        draw_list->AddLine(separator_start, separator_end, Colors::Primary, 2.0f);
        
        // Header da sidebar com logo
        ImVec2 header_min = window_pos;
        ImVec2 header_max = ImVec2(window_pos.x + Layout::SidebarWidth, window_pos.y + Layout::HeaderHeight);
        draw_list->AddRectFilled(header_min, header_max, Colors::Primary, 0);
        
        // Logo/Título
        ImVec2 logo_pos = ImVec2(window_pos.x + Layout::Padding, window_pos.y + Layout::Padding);
        draw_list->AddText(ImGui::GetFont(), 20.0f, logo_pos, Colors::Text, "CS2 CHEAT");
        
        // Subtítulo
        ImVec2 subtitle_pos = ImVec2(window_pos.x + Layout::Padding, window_pos.y + Layout::Padding + 25);
        draw_list->AddText(ImGui::GetFont(), 12.0f, subtitle_pos, Colors::TextMuted, "Professional Edition");
        
        // Itens da sidebar
        int result = selectedItem;
        float item_y = window_pos.y + Layout::HeaderHeight + Layout::Padding;
        
        // Usar ImGui para detecção de clique
        ImGui::SetCursorPos(ImVec2(0, Layout::HeaderHeight + Layout::Padding));
        
        for (int i = 0; i < itemCount; i++) {
            ImVec2 item_min = ImVec2(window_pos.x + 8, item_y);
            ImVec2 item_max = ImVec2(window_pos.x + Layout::SidebarWidth - 8, item_y + Layout::ItemHeight);
            
            // Botão invisível para detecção de clique
            ImGui::SetCursorPos(ImVec2(8, Layout::HeaderHeight + Layout::Padding + i * (Layout::ItemHeight + 4)));
            
            char button_id[32];
            sprintf_s(button_id, "##sidebar_%d", i);
            
            bool clicked = ImGui::InvisibleButton(button_id, ImVec2(Layout::SidebarWidth - 16, Layout::ItemHeight));
            bool hovered = ImGui::IsItemHovered();
            bool selected = (i == selectedItem);
            
            if (clicked) {
                result = i;
            }
            
            // Cor do item baseada no estado
            ImU32 item_color = Colors::Transparent;
            ImU32 text_color = Colors::TextMuted;
            ImU32 icon_color = Colors::TextMuted;
            
            if (selected) {
                item_color = Colors::Primary;
                text_color = Colors::Text;
                icon_color = Colors::Text;
            } else if (hovered) {
                item_color = Colors::SurfaceHover;
                text_color = Colors::Text;
                icon_color = Colors::Primary;
            }
            
            // Desenhar fundo do item com animação
            if (item_color != Colors::Transparent) {
                draw_list->AddRectFilled(item_min, item_max, item_color, Layout::Rounding);
                
                // Borda esquerda para item selecionado
                if (selected) {
                    ImVec2 border_start = ImVec2(item_min.x, item_min.y + 4);
                    ImVec2 border_end = ImVec2(item_min.x, item_max.y - 4);
                    draw_list->AddLine(border_start, border_end, Colors::Text, 3.0f);
                }
            }
            
            // Desenhar ícone
            ImVec2 icon_pos = ImVec2(item_min.x + Layout::Padding, item_min.y + (Layout::ItemHeight - 16) * 0.5f);
            draw_list->AddText(ImGui::GetFont(), 16.0f, icon_pos, icon_color, items[i].icon);
            
            // Desenhar texto
            ImVec2 text_pos = ImVec2(icon_pos.x + 32, item_min.y + (Layout::ItemHeight - 14) * 0.5f);
            draw_list->AddText(ImGui::GetFont(), 14.0f, text_pos, text_color, items[i].label);
            
            item_y += Layout::ItemHeight + 4;
        }
        
        return result;
    }
    
    // Toggle switch moderno
    static bool ModernToggle(const char* label, bool* value, ImVec2 size = ImVec2(50, 24)) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 pos = ImGui::GetCursorScreenPos();
        
        // Área clicável invisível
        ImGui::InvisibleButton(label, size);
        bool clicked = ImGui::IsItemClicked();
        bool hovered = ImGui::IsItemHovered();
        
        if (clicked) *value = !*value;
        
        // Cores baseadas no estado
        ImU32 bg_color = *value ? Colors::Primary : Colors::Secondary;
        ImU32 handle_color = Colors::Text;
        
        if (hovered) {
            bg_color = *value ? Colors::PrimaryHover : Colors::SurfaceHover;
        }
        
        // Desenhar fundo do toggle
        float radius = size.y * 0.5f;
        draw_list->AddRectFilled(pos, ImVec2(pos.x + size.x, pos.y + size.y), bg_color, radius);
        
        // Desenhar handle (círculo)
        float handle_radius = radius - 2;
        float handle_x = *value ? pos.x + size.x - radius : pos.x + radius;
        ImVec2 handle_center = ImVec2(handle_x, pos.y + radius);
        draw_list->AddCircleFilled(handle_center, handle_radius, handle_color);
        
        // Label
        ImGui::SameLine();
        ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 2);
        ImGui::PushStyleColor(ImGuiCol_Text, *value ? Colors::Success : Colors::TextMuted);
        ImGui::Text("%s", label);
        ImGui::PopStyleColor();
        
        return clicked;
    }
    
    // Slider moderno
    static bool ModernSlider(const char* label, float* value, float min_val, float max_val, const char* format = "%.1f") {
        ImGui::PushStyleColor(ImGuiCol_FrameBg, Colors::Surface);
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, Colors::SurfaceHover);
        ImGui::PushStyleColor(ImGuiCol_FrameBgActive, Colors::SurfaceHover);
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, Colors::Primary);
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, Colors::PrimaryHover);
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, Layout::Rounding);
        ImGui::PushStyleVar(ImGuiStyleVar_GrabRounding, Layout::Rounding);
        
        bool result = ImGui::SliderFloat(label, value, min_val, max_val, format);
        
        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor(5);
        
        return result;
    }
    
    // Botão moderno
    static bool ModernButton(const char* label, ImVec2 size = ImVec2(0, 32)) {
        ImGui::PushStyleColor(ImGuiCol_Button, Colors::Primary);
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, Colors::PrimaryHover);
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, Colors::Primary);
        ImGui::PushStyleColor(ImGuiCol_Text, Colors::Text);
        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, Layout::Rounding);
        
        bool result = ImGui::Button(label, size);
        
        ImGui::PopStyleVar();
        ImGui::PopStyleColor(4);
        
        return result;
    }
    
    // Card container melhorado
    static void BeginCard(const char* title = nullptr, ImVec2 size = ImVec2(0, 0)) {
        ImGui::PushStyleColor(ImGuiCol_ChildBg, Colors::Surface);
        ImGui::PushStyleColor(ImGuiCol_Border, Colors::Border);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, Layout::Rounding);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(Layout::Padding, Layout::Padding));
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 1.0f);
        
        // Se não especificado, usar tamanho automático
        if (size.x == 0 && size.y == 0) {
            size = ImVec2(-1, 0); // Largura total, altura automática
        }
        
        ImGui::BeginChild("##card", size, true);
        
        if (title) {
            // Título com ícone
            ImGui::PushStyleColor(ImGuiCol_Text, Colors::Primary);
            ImGui::Text("▶ %s", title);
            ImGui::PopStyleColor();
            
            // Linha decorativa
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            ImVec2 line_start = ImGui::GetCursorScreenPos();
            ImVec2 line_end = ImVec2(line_start.x + ImGui::GetContentRegionAvail().x, line_start.y);
            draw_list->AddLine(line_start, line_end, Colors::Primary, 1.0f);
            
            ImGui::Spacing();
            ImGui::Spacing();
        }
    }
    
    static void EndCard() {
        ImGui::EndChild();
        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(2);
        ImGui::Spacing(); // Espaço entre cards
    }
    
    // Status indicator
    static void StatusIndicator(const char* label, bool active) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 pos = ImGui::GetCursorScreenPos();
        
        // Círculo de status
        float radius = 4.0f;
        ImU32 color = active ? Colors::Success : Colors::Error;
        draw_list->AddCircleFilled(ImVec2(pos.x + radius, pos.y + ImGui::GetTextLineHeight() * 0.5f), radius, color);
        
        // Texto
        ImGui::SetCursorPosX(ImGui::GetCursorPosX() + radius * 2 + 8);
        ImGui::PushStyleColor(ImGuiCol_Text, active ? Colors::Success : Colors::TextMuted);
        ImGui::Text("%s", label);
        ImGui::PopStyleColor();
    }
}
