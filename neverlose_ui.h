#pragma once
#include "imgui.h"
#include <string>
#include <vector>

namespace NeverloseUI {
    // Estrutura para tabs da sidebar
    struct Tab {
        std::string name;
        std::string icon;
        bool selected;
        std::vector<std::string> subtabs;
    };

    // Estrutura para configurações
    struct Setting {
        std::string name;
        bool enabled;
        bool hasSettings;
        float value;
        float minValue;
        float maxValue;
    };

    // Estado global da UI
    static int selectedTab = 0;
    static int selectedSubtab = 0;
    static std::string currentWeapon = "Global";

    // Variáveis para drag do menu
    static bool isDragging = false;
    static ImVec2 dragOffset = ImVec2(0, 0);

    // Tabs principais
    static std::vector<Tab> mainTabs = {
        {"Aimbot", "🎯", true, {"Rage", "Legit"}},
        {"Common", "👁", false, {"Visuals", "Inventory", "Misc"}},
        {"Presets", "⚙", false, {"Config", "Script"}}
    };

    // Configurações do Aimbot
    static std::vector<Setting> aimbotSettings = {
        {"Enabled", true, true, 0, 0, 1},
        {"Silent Aim", true, true, 0, 0, 1},
        {"Auto Fire", true, false, 0, 0, 1},
        {"Auto Wall", true, true, 0, 0, 1},
        {"Field of View", false, false, 180, 0, 360}
    };

    // Função para desenhar um botão de configuração (ícone de engrenagem)
    void DrawSettingButton(float size = 16.0f) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(1, 1, 1, 0.2f));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));

        if (ImGui::Button("⚙", ImVec2(size, size))) {
            // Abrir configurações
        }

        ImGui::PopStyleColor(4);
    }

    // Função para desenhar toggle switch customizado (como no design)
    bool DrawCustomToggle(const char* label, bool* value) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 p = ImGui::GetCursorScreenPos();
        ImVec2 size = ImVec2(24, 12);

        // Cores do toggle
        ImU32 bg_color = *value ? IM_COL32(82, 123, 251, 255) : IM_COL32(60, 60, 70, 255);
        ImU32 circle_color = IM_COL32(255, 255, 255, 255);

        // Desenhar fundo do toggle
        draw_list->AddRectFilled(p, ImVec2(p.x + size.x, p.y + size.y), bg_color, size.y * 0.5f);

        // Desenhar círculo
        float circle_radius = size.y * 0.4f;
        float circle_x = *value ? p.x + size.x - circle_radius - 2 : p.x + circle_radius + 2;
        draw_list->AddCircleFilled(ImVec2(circle_x, p.y + size.y * 0.5f), circle_radius, circle_color);

        // Área clicável invisível
        ImGui::InvisibleButton(label, size);
        bool clicked = ImGui::IsItemClicked();

        if (clicked) {
            *value = !*value;
        }

        return clicked;
    }

    // Função para desenhar slider customizado (estilo Neverlose)
    bool DrawCustomSlider(const char* label, float* value, float min, float max) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 p = ImGui::GetCursorScreenPos();

        // Desenhar label
        ImGui::Text("%s", label);

        // Posicionar slider
        ImGui::SameLine();
        float windowWidth = ImGui::GetWindowWidth();
        float sliderWidth = 100.0f;
        float valueWidth = 30.0f;
        ImGui::SetCursorPosX(windowWidth - sliderWidth - valueWidth - 30);

        // Atualizar posição após SetCursorPosX
        p = ImGui::GetCursorScreenPos();

        // Cores do slider
        ImU32 bg_color = IM_COL32(60, 60, 70, 255);
        ImU32 fill_color = IM_COL32(82, 123, 251, 255);
        ImU32 handle_color = IM_COL32(255, 255, 255, 255);

        // Calcular posição do handle
        float ratio = (*value - min) / (max - min);
        float handle_pos = p.x + ratio * sliderWidth;

        // Desenhar fundo do slider
        draw_list->AddRectFilled(p, ImVec2(p.x + sliderWidth, p.y + 4), bg_color, 2.0f);

        // Desenhar preenchimento
        if (ratio > 0) {
            draw_list->AddRectFilled(p, ImVec2(handle_pos, p.y + 4), fill_color, 2.0f);
        }

        // Desenhar handle
        draw_list->AddCircleFilled(ImVec2(handle_pos, p.y + 2), 6, handle_color);

        // Área clicável
        ImGui::InvisibleButton("##slider", ImVec2(sliderWidth, 12));

        bool changed = false;
        if (ImGui::IsItemActive() && ImGui::IsMouseDragging(0)) {
            ImVec2 mouse_pos = ImGui::GetMousePos();
            float new_ratio = (mouse_pos.x - p.x) / sliderWidth;
            new_ratio = ImClamp(new_ratio, 0.0f, 1.0f);
            *value = min + new_ratio * (max - min);
            changed = true;
        }

        // Mostrar valor
        ImGui::SameLine();
        ImGui::Text("%.0f", *value);

        return changed;
    }

    // Função para desenhar a sidebar (seguindo design exato)
    void DrawSidebar() {
        ImGui::BeginChild("Sidebar", ImVec2(175, 0), false);

        // Logo/Título NEVERLOSE com fonte maior
        ImGui::SetCursorPosY(15);
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 1));

        // Usar fonte maior para o título
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Fonte padrão maior
        float windowWidth = ImGui::GetWindowWidth();
        float textWidth = ImGui::CalcTextSize("NEVERLOSE").x;
        ImGui::SetCursorPosX((windowWidth - textWidth) * 0.5f);
        ImGui::Text("NEVERLOSE");
        ImGui::PopFont();

        ImGui::PopStyleColor();
        ImGui::Spacing();
        ImGui::Spacing();

        // Seção Aimbot
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
        ImGui::Text("Aimbot");
        ImGui::PopStyleColor();

        ImGui::Spacing();

        // Botões Rage e Legit com fonte maior
        for (int j = 0; j < 2; j++) {
            bool isSelected = (selectedTab == 0 && selectedSubtab == j);
            std::string buttonText = (j == 0) ? "🎯 Rage" : "🎯 Legit";

            if (isSelected) {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.32f, 0.48f, 0.98f, 0.3f));
            } else {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
            }

            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.32f, 0.48f, 0.98f, 0.2f));
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.32f, 0.48f, 0.98f, 0.4f));
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 1));

            // Botão maior para melhor visibilidade
            if (ImGui::Button(buttonText.c_str(), ImVec2(155, 32))) {
                selectedTab = 0;
                selectedSubtab = j;
            }

            ImGui::PopStyleColor(4);
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Seção Common
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
        ImGui::Text("Common");
        ImGui::PopStyleColor();

        ImGui::Spacing();

        // Botões Visuals, Inventory, Misc com fonte maior
        const char* commonItems[] = {"👁 Visuals", "📦 Inventory", "⚙ Misc"};
        for (int j = 0; j < 3; j++) {
            bool isSelected = (selectedTab == 1 && selectedSubtab == j);

            if (isSelected) {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.32f, 0.48f, 0.98f, 0.3f));
            } else {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
            }

            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.32f, 0.48f, 0.98f, 0.2f));
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.32f, 0.48f, 0.98f, 0.4f));
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 1));

            // Botão maior para melhor visibilidade
            if (ImGui::Button(commonItems[j], ImVec2(155, 32))) {
                selectedTab = 1;
                selectedSubtab = j;
            }

            ImGui::PopStyleColor(4);
        }

        ImGui::Spacing();
        ImGui::Spacing();

        // Seção Presets
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.6f, 0.6f, 0.6f, 1.0f));
        ImGui::Text("Presets");
        ImGui::PopStyleColor();

        ImGui::Spacing();

        // Botões Config e Script com fonte maior
        const char* presetItems[] = {"🔧 Config", "📜 Script"};
        for (int j = 0; j < 2; j++) {
            bool isSelected = (selectedTab == 2 && selectedSubtab == j);

            if (isSelected) {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.32f, 0.48f, 0.98f, 0.3f));
            } else {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
            }

            ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.32f, 0.48f, 0.98f, 0.2f));
            ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.32f, 0.48f, 0.98f, 0.4f));
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 1));

            // Botão maior para melhor visibilidade
            if (ImGui::Button(presetItems[j], ImVec2(155, 32))) {
                selectedTab = 2;
                selectedSubtab = j;
            }

            ImGui::PopStyleColor(4);
        }

        // User info na parte inferior
        ImGui::SetCursorPosY(ImGui::GetWindowHeight() - 80);

        // Avatar (círculo azul)
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 avatar_pos = ImGui::GetCursorScreenPos();
        draw_list->AddCircleFilled(ImVec2(avatar_pos.x + 20, avatar_pos.y + 20), 18, IM_COL32(82, 123, 251, 255));
        draw_list->AddText(ImVec2(avatar_pos.x + 15, avatar_pos.y + 13), IM_COL32(255, 255, 255, 255), "x");

        ImGui::SetCursorPosX(50);
        ImGui::BeginGroup();
        ImGui::Text("xewoiy");
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.72f, 0.0f, 1.0f)); // Cor dourada
        ImGui::Text("Alpha build v318");
        ImGui::PopStyleColor();
        ImGui::EndGroup();

        ImGui::EndChild();
    }

    // Função para desenhar o header principal
    void DrawHeader() {
        ImGui::BeginChild("Header", ImVec2(0, 50), true);
        
        // Botão Save
        ImGui::SetCursorPos(ImVec2(20, 15));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        if (ImGui::Button("💾 Save")) {
            // Salvar configurações
        }
        ImGui::PopStyleColor(2);
        
        // Dropdown de arma
        ImGui::SameLine();
        ImGui::SetCursorPosX(100);
        ImGui::PushItemWidth(100);
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.22f, 0.5f));
        
        const char* weapons[] = {"Global", "AK-47", "M4A4", "AWP"};
        static int currentWeaponIndex = 0;
        if (ImGui::Combo("##weapon", &currentWeaponIndex, weapons, IM_ARRAYSIZE(weapons))) {
            currentWeapon = weapons[currentWeaponIndex];
        }
        
        ImGui::PopStyleColor();
        ImGui::PopItemWidth();
        
        // Botões da direita
        float windowWidth = ImGui::GetWindowWidth();
        ImGui::SetCursorPos(ImVec2(windowWidth - 80, 15));
        
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        
        if (ImGui::Button("☰")) {
            // Menu
        }
        ImGui::SameLine();
        if (ImGui::Button("🔍")) {
            // Busca
        }
        
        ImGui::PopStyleColor(2);
        
        ImGui::EndChild();
    }

    // Função para desenhar o conteúdo principal
    void DrawMainContent() {
        ImGui::BeginChild("MainContent", ImVec2(0, 0), false);
        
        // Dividir em duas colunas
        float windowWidth = ImGui::GetWindowWidth();
        float columnWidth = (windowWidth - 20) / 2;
        
        // Coluna esquerda
        ImGui::BeginChild("LeftColumn", ImVec2(columnWidth, 0), false);
        
        // Seção MAIN com fonte maior
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.4f)); // Mais visível
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Fonte maior
        ImGui::Text("MAIN");
        ImGui::PopFont();
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("MainSection", ImVec2(0, 180), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        for (int i = 0; i < 4; i++) {
            Setting& setting = aimbotSettings[i];

            // Texto maior e mais visível
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.9f)); // Mais opaco
            ImGui::Text("%s", setting.name.c_str());
            ImGui::PopStyleColor();

            // Posicionar controles à direita
            float contentWidth = ImGui::GetWindowWidth();

            // Botão de configuração (se disponível)
            if (setting.hasSettings) {
                ImGui::SameLine();
                ImGui::SetCursorPosX(contentWidth - 55);
                DrawSettingButton();
            }

            // Toggle switch
            ImGui::SameLine();
            ImGui::SetCursorPosX(contentWidth - 30);
            DrawCustomToggle(("##toggle" + std::to_string(i)).c_str(), &setting.enabled);

            ImGui::Spacing();
            ImGui::Spacing(); // Espaçamento extra
        }
        
        // Field of View slider
        Setting& fovSetting = aimbotSettings[4];
        DrawCustomSlider(fovSetting.name.c_str(), &fovSetting.value, fovSetting.minValue, fovSetting.maxValue);
        
        ImGui::EndChild();
        
        // Seção SELECTION com fonte maior
        ImGui::Spacing();
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.4f)); // Mais visível
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Fonte maior
        ImGui::Text("SELECTION");
        ImGui::PopFont();
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("SelectionSection", ImVec2(0, 60), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        ImGui::Text("Target");
        ImGui::SameLine();
        float contentWidth = ImGui::GetWindowWidth();
        ImGui::SetCursorPosX(contentWidth - 30);
        DrawSettingButton();
        
        ImGui::EndChild();
        
        ImGui::EndChild();
        
        // Coluna direita
        ImGui::SameLine();
        ImGui::BeginChild("RightColumn", ImVec2(columnWidth, 0), false);
        
        // Seção MAIN (direita) com fonte maior
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.4f)); // Mais visível
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Fonte maior
        ImGui::Text("MAIN");
        ImGui::PopFont();
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("MainSectionRight", ImVec2(0, 120), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        // Configurações duplicadas para a coluna direita (seguindo design)
        for (int i = 0; i < 3; i++) {
            Setting& setting = aimbotSettings[i];

            ImGui::Text("%s", setting.name.c_str());

            float contentWidth = ImGui::GetWindowWidth();

            // Botão de configuração (se disponível)
            if (setting.hasSettings) {
                ImGui::SameLine();
                ImGui::SetCursorPosX(contentWidth - 55);
                DrawSettingButton();
            }

            // Toggle switch
            ImGui::SameLine();
            ImGui::SetCursorPosX(contentWidth - 30);
            DrawCustomToggle(("##toggleRight" + std::to_string(i)).c_str(), &setting.enabled);

            ImGui::Spacing();
        }
        
        ImGui::EndChild();
        
        ImGui::EndChild();
        
        ImGui::EndChild();
    }

    // Função principal para desenhar a UI Neverlose
    void DrawNeverloseUI() {
        // Configurar janela principal
        // Configurar estilo da janela para seguir design exato
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(8, 8));

        // Configurar tamanhos de fonte maiores
        ImGuiStyle& style = ImGui::GetStyle();
        float originalFontScale = style.ScaleAllSizes;
        style.ScaleAllSizes = 1.2f; // Aumentar tamanho geral

        ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.11f, 0.11f, 0.13f, 1.0f)); // Fundo escuro exato
        ImGui::PushStyleColor(ImGuiCol_ChildBg, ImVec4(0.11f, 0.11f, 0.13f, 1.0f));

        ImGuiWindowFlags windowFlags = ImGuiWindowFlags_NoResize |
                                      ImGuiWindowFlags_NoCollapse |
                                      ImGuiWindowFlags_NoScrollbar |
                                      ImGuiWindowFlags_NoScrollWithMouse |
                                      ImGuiWindowFlags_NoTitleBar;

        ImGui::SetNextWindowSize(ImVec2(700, 500), ImGuiCond_Always);

        if (ImGui::Begin("NEVERLOSE", nullptr, windowFlags)) {
            // Implementar drag do menu
            ImVec2 windowPos = ImGui::GetWindowPos();
            ImVec2 windowSize = ImGui::GetWindowSize();
            ImVec2 mousePos = ImGui::GetMousePos();

            // Área de drag (toda a janela)
            bool isMouseOverWindow = (mousePos.x >= windowPos.x && mousePos.x <= windowPos.x + windowSize.x &&
                                    mousePos.y >= windowPos.y && mousePos.y <= windowPos.y + windowSize.y);

            if (isMouseOverWindow && ImGui::IsMouseClicked(0)) {
                isDragging = true;
                dragOffset = ImVec2(mousePos.x - windowPos.x, mousePos.y - windowPos.y);
            }

            if (isDragging) {
                if (ImGui::IsMouseDown(0)) {
                    ImVec2 newPos = ImVec2(mousePos.x - dragOffset.x, mousePos.y - dragOffset.y);
                    ImGui::SetWindowPos(newPos);
                } else {
                    isDragging = false;
                }
            }
            // Layout horizontal: Sidebar + Conteúdo principal
            DrawSidebar();
            
            ImGui::SameLine();
            
            // Área principal
            ImGui::BeginChild("MainArea", ImVec2(520, 0), false);

            DrawHeader();
            DrawMainContent();

            ImGui::EndChild();
        }

        // Restaurar scale original
        style.ScaleAllSizes = originalFontScale;

        ImGui::PopStyleColor(2);
        ImGui::PopStyleVar(3);
        ImGui::End();
    }
}
