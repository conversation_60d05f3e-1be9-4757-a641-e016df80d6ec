#pragma once
#include "imgui.h"
#include <string>
#include <vector>

namespace NeverloseUI {
    // Estrutura para tabs da sidebar
    struct Tab {
        std::string name;
        std::string icon;
        bool selected;
        std::vector<std::string> subtabs;
    };

    // Estrutura para configurações
    struct Setting {
        std::string name;
        bool enabled;
        bool hasSettings;
        float value;
        float minValue;
        float maxValue;
    };

    // Estado global da UI
    static int selectedTab = 0;
    static int selectedSubtab = 0;
    static std::string currentWeapon = "Global";

    // Tabs principais
    static std::vector<Tab> mainTabs = {
        {"Aimbot", "🎯", true, {"Rage", "Legit"}},
        {"Common", "👁", false, {"Visuals", "Inventory", "Misc"}},
        {"Presets", "⚙", false, {"Config", "Script"}}
    };

    // Configurações do Aimbot
    static std::vector<Setting> aimbotSettings = {
        {"Enabled", true, true, 0, 0, 1},
        {"Silent Aim", true, true, 0, 0, 1},
        {"Auto Fire", true, false, 0, 0, 1},
        {"Auto Wall", true, true, 0, 0, 1},
        {"Field of View", false, false, 180, 0, 360}
    };

    // Função para desenhar um botão de configuração
    void DrawSettingButton(float size = 15.0f) {
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(1, 1, 1, 0.2f));
        
        if (ImGui::Button("⚙", ImVec2(size, size))) {
            // Abrir configurações
        }
        
        ImGui::PopStyleColor(3);
    }

    // Função para desenhar checkbox customizado
    bool DrawCustomCheckbox(const char* label, bool* value) {
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.32f, 0.48f, 0.98f, 1.0f));
        
        bool changed = ImGui::Checkbox("", value);
        
        ImGui::PopStyleColor(4);
        
        ImGui::SameLine();
        ImGui::Text("%s", label);
        
        return changed;
    }

    // Função para desenhar slider customizado
    bool DrawCustomSlider(const char* label, float* value, float min, float max) {
        ImGui::Text("%s", label);
        ImGui::SameLine();
        
        // Posicionar slider à direita
        float windowWidth = ImGui::GetWindowWidth();
        float sliderWidth = 120.0f;
        float valueWidth = 30.0f;
        ImGui::SetCursorPosX(windowWidth - sliderWidth - valueWidth - 20);
        
        ImGui::PushItemWidth(sliderWidth);
        ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(1, 1, 1, 1));
        ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(0.32f, 0.48f, 0.98f, 1));
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.32f, 0.48f, 0.98f, 0.5f));
        
        bool changed = ImGui::SliderFloat("##slider", value, min, max, "");
        
        ImGui::PopStyleColor(3);
        ImGui::PopItemWidth();
        
        ImGui::SameLine();
        ImGui::Text("%.0f", *value);
        
        return changed;
    }

    // Função para desenhar a sidebar
    void DrawSidebar() {
        ImGui::BeginChild("Sidebar", ImVec2(175, 0), true);
        
        // Logo/Título
        ImGui::SetCursorPosY(20);
        ImGui::PushFont(nullptr); // Usar fonte padrão por enquanto
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 1));
        
        // Centralizar o título
        float windowWidth = ImGui::GetWindowWidth();
        float textWidth = ImGui::CalcTextSize("NEVERLOSE").x;
        ImGui::SetCursorPosX((windowWidth - textWidth) * 0.5f);
        ImGui::Text("NEVERLOSE");
        
        ImGui::PopStyleColor();
        ImGui::PopFont();
        
        ImGui::Spacing();
        ImGui::Spacing();
        
        // Desenhar tabs
        for (int i = 0; i < mainTabs.size(); i++) {
            Tab& tab = mainTabs[i];
            
            // Cabeçalho da categoria
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.5f));
            ImGui::Text("%s", tab.name.c_str());
            ImGui::PopStyleColor();
            
            ImGui::Spacing();
            
            // Subtabs
            for (int j = 0; j < tab.subtabs.size(); j++) {
                bool isSelected = (selectedTab == i && selectedSubtab == j);
                
                if (isSelected) {
                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.15f, 0.15f, 0.22f, 0.5f));
                } else {
                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
                }
                
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.20f, 0.20f, 0.30f, 0.6f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.32f, 0.48f, 0.98f, 0.5f));
                
                std::string buttonText = tab.icon + " " + tab.subtabs[j];
                if (ImGui::Button(buttonText.c_str(), ImVec2(155, 30))) {
                    selectedTab = i;
                    selectedSubtab = j;
                }
                
                ImGui::PopStyleColor(3);
            }
            
            ImGui::Spacing();
            ImGui::Spacing();
        }
        
        // User info na parte inferior
        ImGui::SetCursorPosY(ImGui::GetWindowHeight() - 60);
        ImGui::Separator();
        ImGui::Spacing();
        
        // Avatar placeholder
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.32f, 0.48f, 0.98f, 1));
        ImGui::Button("👤", ImVec2(40, 40));
        ImGui::PopStyleColor();
        
        ImGui::SameLine();
        ImGui::BeginGroup();
        ImGui::Text("xewoiy");
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.72f, 0.0f, 1.0f)); // Cor dourada
        ImGui::Text("Alpha build v318");
        ImGui::PopStyleColor();
        ImGui::EndGroup();
        
        ImGui::EndChild();
    }

    // Função para desenhar o header principal
    void DrawHeader() {
        ImGui::BeginChild("Header", ImVec2(0, 50), true);
        
        // Botão Save
        ImGui::SetCursorPos(ImVec2(20, 15));
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        if (ImGui::Button("💾 Save")) {
            // Salvar configurações
        }
        ImGui::PopStyleColor(2);
        
        // Dropdown de arma
        ImGui::SameLine();
        ImGui::SetCursorPosX(100);
        ImGui::PushItemWidth(100);
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.22f, 0.5f));
        
        const char* weapons[] = {"Global", "AK-47", "M4A4", "AWP"};
        static int currentWeaponIndex = 0;
        if (ImGui::Combo("##weapon", &currentWeaponIndex, weapons, IM_ARRAYSIZE(weapons))) {
            currentWeapon = weapons[currentWeaponIndex];
        }
        
        ImGui::PopStyleColor();
        ImGui::PopItemWidth();
        
        // Botões da direita
        float windowWidth = ImGui::GetWindowWidth();
        ImGui::SetCursorPos(ImVec2(windowWidth - 80, 15));
        
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0, 0, 0, 0));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1, 1, 1, 0.1f));
        
        if (ImGui::Button("☰")) {
            // Menu
        }
        ImGui::SameLine();
        if (ImGui::Button("🔍")) {
            // Busca
        }
        
        ImGui::PopStyleColor(2);
        
        ImGui::EndChild();
    }

    // Função para desenhar o conteúdo principal
    void DrawMainContent() {
        ImGui::BeginChild("MainContent", ImVec2(0, 0), false);
        
        // Dividir em duas colunas
        float windowWidth = ImGui::GetWindowWidth();
        float columnWidth = (windowWidth - 20) / 2;
        
        // Coluna esquerda
        ImGui::BeginChild("LeftColumn", ImVec2(columnWidth, 0), false);
        
        // Seção MAIN
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.25f));
        ImGui::Text("MAIN");
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("MainSection", ImVec2(0, 180), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        for (int i = 0; i < 4; i++) {
            Setting& setting = aimbotSettings[i];
            
            ImGui::Text("%s", setting.name.c_str());
            ImGui::SameLine();
            
            // Posicionar controles à direita
            float contentWidth = ImGui::GetWindowWidth();
            if (setting.hasSettings) {
                ImGui::SetCursorPosX(contentWidth - 50);
                DrawSettingButton();
                ImGui::SameLine();
            }
            
            ImGui::SetCursorPosX(contentWidth - 25);
            DrawCustomCheckbox("", &setting.enabled);
            
            ImGui::Spacing();
        }
        
        // Field of View slider
        Setting& fovSetting = aimbotSettings[4];
        DrawCustomSlider(fovSetting.name.c_str(), &fovSetting.value, fovSetting.minValue, fovSetting.maxValue);
        
        ImGui::EndChild();
        
        // Seção SELECTION
        ImGui::Spacing();
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.25f));
        ImGui::Text("SELECTION");
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("SelectionSection", ImVec2(0, 60), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        ImGui::Text("Target");
        ImGui::SameLine();
        float contentWidth = ImGui::GetWindowWidth();
        ImGui::SetCursorPosX(contentWidth - 30);
        DrawSettingButton();
        
        ImGui::EndChild();
        
        ImGui::EndChild();
        
        // Coluna direita
        ImGui::SameLine();
        ImGui::BeginChild("RightColumn", ImVec2(columnWidth, 0), false);
        
        // Seção MAIN (direita)
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1, 1, 1, 0.25f));
        ImGui::Text("MAIN");
        ImGui::PopStyleColor();
        
        ImGui::BeginChild("MainSectionRight", ImVec2(0, 120), true);
        ImGui::SetCursorPos(ImVec2(10, 10));
        
        // Configurações duplicadas para a coluna direita
        for (int i = 0; i < 3; i++) {
            Setting& setting = aimbotSettings[i];
            
            ImGui::Text("%s", setting.name.c_str());
            ImGui::SameLine();
            
            float contentWidth = ImGui::GetWindowWidth();
            if (setting.hasSettings) {
                ImGui::SetCursorPosX(contentWidth - 50);
                DrawSettingButton();
                ImGui::SameLine();
            }
            
            ImGui::SetCursorPosX(contentWidth - 25);
            DrawCustomCheckbox("", &setting.enabled);
            
            ImGui::Spacing();
        }
        
        ImGui::EndChild();
        
        ImGui::EndChild();
        
        ImGui::EndChild();
    }

    // Função principal para desenhar a UI Neverlose
    void DrawNeverloseUI() {
        // Configurar janela principal
        ImGuiWindowFlags windowFlags = ImGuiWindowFlags_NoResize | 
                                      ImGuiWindowFlags_NoCollapse |
                                      ImGuiWindowFlags_NoScrollbar |
                                      ImGuiWindowFlags_NoScrollWithMouse;
        
        ImGui::SetNextWindowSize(ImVec2(675, 500), ImGuiCond_Always);
        
        if (ImGui::Begin("NEVERLOSE", nullptr, windowFlags)) {
            // Layout horizontal: Sidebar + Conteúdo principal
            DrawSidebar();
            
            ImGui::SameLine();
            
            // Área principal
            ImGui::BeginChild("MainArea", ImVec2(500, 0), false);
            
            DrawHeader();
            DrawMainContent();
            
            ImGui::EndChild();
        }
        ImGui::End();
    }
}
