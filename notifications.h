#pragma once
#include "imgui.h"
#include "modern_ui.h"
#include <vector>
#include <string>

namespace Notifications {
    enum Type {
        Info,
        Success,
        Warning,
        Error
    };
    
    struct Notification {
        std::string message;
        Type type;
        float time_remaining;
        float alpha;
    };
    
    static std::vector<Notification> notifications;
    static const float NOTIFICATION_DURATION = 3.0f;
    
    // Adicionar notificação
    static void Add(const std::string& message, Type type = Info) {
        Notification notif;
        notif.message = message;
        notif.type = type;
        notif.time_remaining = NOTIFICATION_DURATION;
        notif.alpha = 0.0f;
        
        notifications.push_back(notif);
        
        // Limitar a 5 notificações
        if (notifications.size() > 5) {
            notifications.erase(notifications.begin());
        }
    }
    
    // Atualizar e renderizar notificações
    static void Update() {
        float deltaTime = ImGui::GetIO().DeltaTime;
        
        // Atualizar notificações
        for (auto it = notifications.begin(); it != notifications.end();) {
            it->time_remaining -= deltaTime;
            
            // Fade in/out
            if (it->time_remaining > NOTIFICATION_DURATION - 0.5f) {
                it->alpha = (NOTIFICATION_DURATION - it->time_remaining) * 2.0f;
            } else if (it->time_remaining < 0.5f) {
                it->alpha = it->time_remaining * 2.0f;
            } else {
                it->alpha = 1.0f;
            }
            
            if (it->time_remaining <= 0) {
                it = notifications.erase(it);
            } else {
                ++it;
            }
        }
        
        // Renderizar notificações
        if (!notifications.empty()) {
            ImVec2 screen_size = ImGui::GetIO().DisplaySize;
            float notification_width = 300.0f;
            float notification_height = 60.0f;
            float start_y = 50.0f;
            
            for (size_t i = 0; i < notifications.size(); i++) {
                const Notification& notif = notifications[i];
                
                ImVec2 pos = ImVec2(
                    screen_size.x - notification_width - 20,
                    start_y + i * (notification_height + 10)
                );
                
                // Cor baseada no tipo
                ImU32 bg_color = ModernUI::Colors::Surface;
                ImU32 border_color = ModernUI::Colors::Primary;
                const char* icon = "ℹ";
                
                switch (notif.type) {
                    case Success:
                        border_color = ModernUI::Colors::Success;
                        icon = "✓";
                        break;
                    case Warning:
                        border_color = ModernUI::Colors::Warning;
                        icon = "⚠";
                        break;
                    case Error:
                        border_color = ModernUI::Colors::Error;
                        icon = "✗";
                        break;
                }
                
                // Aplicar alpha
                bg_color = (bg_color & 0x00FFFFFF) | ((int)(notif.alpha * 255) << 24);
                border_color = (border_color & 0x00FFFFFF) | ((int)(notif.alpha * 255) << 24);
                ImU32 text_color = (ModernUI::Colors::Text & 0x00FFFFFF) | ((int)(notif.alpha * 255) << 24);
                
                ImDrawList* draw_list = ImGui::GetForegroundDrawList();
                
                // Fundo da notificação
                draw_list->AddRectFilled(
                    pos, 
                    ImVec2(pos.x + notification_width, pos.y + notification_height),
                    bg_color,
                    ModernUI::Layout::Rounding
                );
                
                // Borda
                draw_list->AddRect(
                    pos,
                    ImVec2(pos.x + notification_width, pos.y + notification_height),
                    border_color,
                    ModernUI::Layout::Rounding,
                    0,
                    2.0f
                );
                
                // Ícone
                draw_list->AddText(
                    ImVec2(pos.x + 15, pos.y + 20),
                    border_color,
                    icon
                );
                
                // Texto
                draw_list->AddText(
                    ImVec2(pos.x + 40, pos.y + 20),
                    text_color,
                    notif.message.c_str()
                );
            }
        }
    }
}
