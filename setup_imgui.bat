@echo off
echo ========================================
echo    CS2 Cheat Menu - ImGui Setup
echo ========================================
echo.

echo Verificando se a pasta imgui existe...
if not exist "imgui" (
    echo [ERRO] Pasta 'imgui' nao encontrada!
    echo.
    echo Por favor, siga estes passos:
    echo 1. Va para https://github.com/ocornut/imgui
    echo 2. Baixe a versao mais recente
    echo 3. Extraia na pasta 'imgui' deste projeto
    echo.
    pause
    exit /b 1
)

echo [OK] Pasta imgui encontrada!
echo.

echo Verificando arquivos essenciais...
set "missing_files="
set "missing_backends="

REM Verificar arquivos principais
if not exist "imgui\imgui.h" set "missing_files=%missing_files% imgui.h"
if not exist "imgui\imgui.cpp" set "missing_files=%missing_files% imgui.cpp"
if not exist "imgui\imgui_draw.cpp" set "missing_files=%missing_files% imgui_draw.cpp"
if not exist "imgui\imgui_tables.cpp" set "missing_files=%missing_files% imgui_tables.cpp"
if not exist "imgui\imgui_widgets.cpp" set "missing_files=%missing_files% imgui_widgets.cpp"

REM Verificar arquivos de backend (podem estar em backends/ ou na raiz)
if not exist "imgui\imgui_impl_dx9.h" (
    if not exist "imgui\backends\imgui_impl_dx9.h" (
        set "missing_backends=%missing_backends% imgui_impl_dx9.h"
    )
)
if not exist "imgui\imgui_impl_dx9.cpp" (
    if not exist "imgui\backends\imgui_impl_dx9.cpp" (
        set "missing_backends=%missing_backends% imgui_impl_dx9.cpp"
    )
)
if not exist "imgui\imgui_impl_win32.h" (
    if not exist "imgui\backends\imgui_impl_win32.h" (
        set "missing_backends=%missing_backends% imgui_impl_win32.h"
    )
)
if not exist "imgui\imgui_impl_win32.cpp" (
    if not exist "imgui\backends\imgui_impl_win32.cpp" (
        set "missing_backends=%missing_backends% imgui_impl_win32.cpp"
    )
)

if not "%missing_files%"=="" (
    echo [ERRO] Arquivos principais faltando:%missing_files%
    echo.
    echo Baixe o ImGui completo de: https://github.com/ocornut/imgui
    pause
    exit /b 1
)

if not "%missing_backends%"=="" (
    echo [AVISO] Arquivos de backend faltando:%missing_backends%
    echo.
    echo Os arquivos de backend estao na pasta backends/ do ImGui.
    echo Copiando da pasta backends/ se existir...
    
    if exist "imgui\backends\imgui_impl_dx9.h" (
        copy "imgui\backends\imgui_impl_dx9.h" "imgui\" >nul
        echo [OK] Copiado imgui_impl_dx9.h
    )
    if exist "imgui\backends\imgui_impl_dx9.cpp" (
        copy "imgui\backends\imgui_impl_dx9.cpp" "imgui\" >nul
        echo [OK] Copiado imgui_impl_dx9.cpp
    )
    if exist "imgui\backends\imgui_impl_win32.h" (
        copy "imgui\backends\imgui_impl_win32.h" "imgui\" >nul
        echo [OK] Copiado imgui_impl_win32.h
    )
    if exist "imgui\backends\imgui_impl_win32.cpp" (
        copy "imgui\backends\imgui_impl_win32.cpp" "imgui\" >nul
        echo [OK] Copiado imgui_impl_win32.cpp
    )
    
    echo.
    echo Verificando novamente...
    
    REM Verificar novamente após cópia
    set "still_missing="
    if not exist "imgui\imgui_impl_dx9.h" set "still_missing=%still_missing% imgui_impl_dx9.h"
    if not exist "imgui\imgui_impl_dx9.cpp" set "still_missing=%still_missing% imgui_impl_dx9.cpp"
    if not exist "imgui\imgui_impl_win32.h" set "still_missing=%still_missing% imgui_impl_win32.h"
    if not exist "imgui\imgui_impl_win32.cpp" set "still_missing=%still_missing% imgui_impl_win32.cpp"
    
    if not "%still_missing%"=="" (
        echo [ERRO] Ainda faltando:%still_missing%
        echo.
        echo SOLUCAO:
        echo 1. Va para https://github.com/ocornut/imgui
        echo 2. Baixe o ZIP completo
        echo 3. Copie estes arquivos da pasta backends/ para imgui/:
        echo    - imgui_impl_dx9.h
        echo    - imgui_impl_dx9.cpp  
        echo    - imgui_impl_win32.h
        echo    - imgui_impl_win32.cpp
        echo.
        pause
        exit /b 1
    )
)

echo [OK] Todos os arquivos essenciais encontrados!
echo.

echo ========================================
echo         Setup Concluido!
echo ========================================
echo.
echo Agora voce pode:
echo 1. Abrir CS2CheatMenu.sln no Visual Studio 2022
echo 2. Compilar o projeto (Ctrl+Shift+B)
echo 3. Executar (F5)
echo.
echo Controles:
echo - INSERT: Mostrar/Ocultar menu
echo - Mouse: Navegar pelo menu
echo.
pause
