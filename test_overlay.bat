@echo off
echo ========================================
echo    Testando CS2 Cheat Menu Overlay
echo ========================================
echo.

echo Compilando...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" CS2CheatMenu.sln /p:Configuration=Debug /p:Platform=Win32 /v:minimal
) else (
    echo [ERRO] MSBuild nao encontrado!
    pause
    exit /b 1
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo         Overlay Pronto!
    echo ========================================
    echo.
    echo CONTROLES:
    echo - INSERT: Mostrar/Ocultar menu
    echo - ESC: Fechar aplicacao
    echo.
    echo O menu aparecera como overlay transparente
    echo sem bordas de janela do Windows.
    echo.
    echo Executando...
    
    if exist "bin\Debug\CS2CheatMenu.exe" (
        start "" "bin\Debug\CS2CheatMenu.exe"
    ) else (
        echo [ERRO] Executavel nao encontrado!
        pause
    )
) else (
    echo.
    echo [ERRO] Falha na compilacao!
    pause
)
