/* Neverlose */

position: relative;
width: 1920px;
height: 1080px;

background: #FFFFFF;


/* image 1 */

position: absolute;
width: 1923px;
height: 1080px;
left: 0px;
top: 0px;

background: url(image.png);


/* Menu */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;

position: absolute;
width: 675px;
height: 500px;
left: calc(50% - 675px/2 - 292.5px);
top: calc(50% - 500px/2 + 11px);

background: rgba(0, 0, 0, 0.75);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;


/* Left Tab */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

width: 175px;
height: 500px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Tab */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 10px 0px;

width: 175px;
height: 450px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 1;


/* NEVERLOSE */

width: 146px;
height: 29px;

font-family: 'Inter';
font-style: normal;
font-weight: 900;
font-size: 24px;
line-height: 29px;
/* identical to box height */

color: #FFFFFF;

text-shadow: 0px 4px 25px #000000;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Aimbot */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 5px;

width: 175px;
height: 102px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Title */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 10px;
gap: 10px;

width: 155px;
height: 12px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Aimbot */

width: 34px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Rage */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

background: rgba(25, 25, 35, 0.5);
backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* mingcute:aiming-line */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.33%;

background: #527BFB;


/* Rage */

width: 29px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Legit */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* material-symbols:mouse-outline */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 20.83%;
right: 20.83%;
top: 8.33%;
bottom: 8.33%;

background: #527BFB;


/* Legit */

width: 29px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Common */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 5px;

width: 175px;
height: 137px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Title */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 10px;
gap: 10px;

width: 155px;
height: 12px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Common */

width: 43px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Visuals */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* mdi:eye-outline */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 4.17%;
right: 4.17%;
top: 18.75%;
bottom: 18.75%;

background: #527BFB;


/* Visuals */

width: 41px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Inventory */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* ci:layer */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 16.67%;
bottom: 16.67%;

border: 2px solid #527BFB;


/* Inventory */

width: 54px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Misc */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* flowbite:list-outline */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 20.75%;
right: 20.83%;
top: 33.33%;
bottom: 33.33%;

border: 2px solid #527BFB;


/* Misc */

width: 27px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Presets */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 5px;

width: 175px;
height: 102px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Title */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 10px;
gap: 10px;

width: 155px;
height: 12px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Presets */

width: 36px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Visuals */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* iconamoon:options */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 25%;
bottom: 25%;



/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 33.33%;
bottom: 33.33%;

border: 2px solid #527BFB;


/* Vector */

position: absolute;
left: 29.17%;
right: 54.17%;
top: 58.33%;
bottom: 25%;

border: 2px solid #527BFB;


/* Vector */

position: absolute;
left: 54.17%;
right: 29.17%;
top: 25%;
bottom: 58.33%;

border: 2px solid #527BFB;


/* Config */

width: 38px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Inventory */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 5px;
gap: 10px;

width: 155px;
height: 30px;

backdrop-filter: blur(50px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* mingcute:paper-line */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.33%;
right: 12.5%;
top: 12.5%;
bottom: 8.33%;

background: #527BFB;


/* Script */

width: 34px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* User Info */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;

width: 175px;
height: 50px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Image */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 5px;
gap: 5px;

width: 50px;
height: 50px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Image */

width: 40px;
height: 40px;

background: url(b4ba4e9d50a07b52918b73d0397a8bae.jpg);
border-radius: 10000px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 1;


/* Info */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: flex-start;
padding: 5px;
gap: 5px;

width: 125px;
height: 50px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 1;


/* xewoiy */

width: 40px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: rgba(255, 255, 255, 0.75);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Alpha build v318 */

width: 79px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFB800;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Main */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;

width: 500px;
height: 500px;

background: rgba(14, 12, 16, 0.75);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Top Bar */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 10px;
gap: 10px;

width: 500px;
height: 50px;

background: rgba(0, 0, 0, 0.1);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Left */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 20px;

margin: 0 auto;
width: 149px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Save */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 5px;

width: 53px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* ci:save */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

border: 2px solid #FFFFFF;


/* Save */

width: 28px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 500;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Weapon */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 2px 10px;
gap: 10px;

width: 76px;
height: 20px;

background: rgba(37, 38, 55, 0.5);
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Global */

width: 36px;
height: 15px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 15px;
/* identical to box height */

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* bx:up-arrow */

width: 10px;
height: 10px;

transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.34%;
right: 8.33%;
top: 20.83%;
bottom: 16.78%;

background: #FFFFFF;
transform: rotate(-180deg);


/* Right */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px 10px;
gap: 10px;

margin: 0 auto;
width: 70px;
height: 30px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* ph:list-bold */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 10.94%;
right: 10.94%;
top: 20.31%;
bottom: 20.31%;

background: #FFFFFF;


/* mingcute:search-line */

width: 20px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Group */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* Vector */

position: absolute;
left: 8.34%;
right: 12.05%;
top: 8.33%;
bottom: 12.05%;

background: #FFFFFF;


/* Main */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 10px;
gap: 5px;

width: 500px;
height: 450px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 1;


/* Left */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;

width: 237.5px;
height: 430px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 1;


/* Main */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 5px;
gap: 5px;

width: 237.5px;
height: 177px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* MAIN */

width: 27px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 5px 10px;
gap: 10px;

width: 227.5px;
height: 150px;

background: rgba(10, 10, 18, 0.5);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Enabled */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Enabled */

margin: 0 auto;
width: 39px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 35px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Silent Aim */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Silent Aim */

margin: 0 auto;
width: 48px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 35px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Auto Fire */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Auto Fire */

margin: 0 auto;
width: 43px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Auto Wall */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Auto Wall */

margin: 0 auto;
width: 45px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 35px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* FOV */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Field of View */

margin: 0 auto;
width: 61px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px 5px;
gap: 5px;

margin: 0 auto;
width: 146.5px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 1;


/* Slider */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 115.5px;
height: 5px;

background: linear-gradient(90deg, rgba(82, 123, 251, 0.5) 0%, rgba(49, 73, 149, 0.5) 100%);
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Slider */

width: 70px;
height: 5px;

background: #FFFFFF;
mix-blend-mode: soft-light;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Weapon */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 16px;
height: 10px;

border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* 180 */

width: 16px;
height: 10px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 8px;
line-height: 10px;
/* identical to box height */
display: flex;
align-items: center;
text-align: center;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Selection */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 5px;
gap: 5px;

width: 237.5px;
height: 57px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* SELECTION */

width: 57px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 5px 10px;
gap: 10px;

width: 227.5px;
height: 30px;

background: rgba(10, 10, 18, 0.5);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Target */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Target */

margin: 0 auto;
width: 31px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Right */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;

width: 237.5px;
height: 430px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 1;


/* Main */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 5px;
gap: 5px;

width: 237.5px;
height: 117px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* MAIN */

width: 27px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 10px;
line-height: 12px;

color: rgba(255, 255, 255, 0.25);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Content */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 5px 10px;
gap: 10px;

width: 227.5px;
height: 90px;

background: rgba(10, 10, 18, 0.5);
backdrop-filter: blur(25px);
/* Note: backdrop-filter has minimal browser support */
border-radius: 5px;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Enabled */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Enabled */

margin: 0 auto;
width: 39px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 35px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Silent Aim */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Silent Aim */

margin: 0 auto;
width: 48px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 35px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* mi:settings */

width: 15px;
height: 15px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;



/* Vector */

position: absolute;
left: 8.32%;
right: 8.32%;
top: 8.32%;
bottom: 8.32%;

background: rgba(255, 255, 255, 0.5);


/* Vector */

position: absolute;
left: 33.54%;
right: 33.13%;
top: 33.13%;
bottom: 33.54%;

background: rgba(255, 255, 255, 0.5);


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Auto Fire */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 2px 0px;
gap: 358px;

width: 207.5px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Auto Fire */

margin: 0 auto;
width: 43px;
height: 12px;

font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 10px;
line-height: 12px;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 10px;

margin: 0 auto;
width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Check Box */

width: 10px;
height: 10px;

background: #527BFB;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


https://www.figma.com/design/cqGRz52TguX1CRsjQOnzO9/CS2-Hacks--Community-?node-id=12-2&t=7a5xxTyyEfb94wyT-4


https://www.figma.com/design/cqGRz52TguX1CRsjQOnzO9/CS2-Hacks--Community-?node-id=12-2&m=dev&t=7a5xxTyyEfb94wyT-1

